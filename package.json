{"name": "football", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint", "server": "node server/index.js", "dev": "concurrently \"npm run serve\" \"npm run server\"", "start": "npm run server"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "axios": "^1.9.0", "core-js": "^3.8.3", "cors": "^2.8.5", "echarts": "^5.6.0", "element-plus": "^2.9.11", "express": "^5.1.0", "mysql2": "^3.14.1", "pinia": "^3.0.2", "vue": "^3.2.13", "vue-echarts": "^7.0.3", "vue-router": "^4.5.1"}, "devDependencies": {"@babel/core": "^7.12.16", "@babel/eslint-parser": "^7.12.16", "@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-plugin-eslint": "~5.0.0", "@vue/cli-service": "~5.0.0", "concurrently": "^9.1.2", "eslint": "^7.32.0", "eslint-plugin-vue": "^8.0.3"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/vue3-essential", "eslint:recommended"], "parserOptions": {"parser": "@babel/eslint-parser"}, "rules": {}}, "browserslist": ["> 1%", "last 2 versions", "not dead", "not ie 11"]}