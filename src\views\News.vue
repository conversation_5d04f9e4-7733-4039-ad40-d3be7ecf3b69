<template>
  <div class="news">
    <div class="page-container">
      <h1 class="page-title">新闻动态</h1>
      
      <!-- 搜索和操作栏 -->
      <div class="toolbar">
        <el-row :gutter="20" justify="space-between">
          <el-col :span="16">
            <el-input
              v-model="searchText"
              placeholder="搜索新闻标题或内容"
              clearable
              @input="handleSearch"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
          </el-col>
          <el-col :span="8" style="text-align: right">
            <el-button type="primary" @click="showAddDialog = true">
              <el-icon><Plus /></el-icon>
              发布新闻
            </el-button>
          </el-col>
        </el-row>
      </div>

      <!-- 新闻列表 -->
      <div class="news-list">
        <div v-if="filteredNews.length === 0" class="empty-state">
          <el-empty description="暂无新闻" />
        </div>
        <div v-else>
          <el-card 
            v-for="article in paginatedNews" 
            :key="article.id" 
            class="news-card"
            shadow="hover"
          >
            <div class="news-content">
              <div class="news-header">
                <h3 class="news-title">{{ article.title }}</h3>
                <div class="news-meta">
                  <el-tag :type="getCategoryType(article.category)" size="small">
                    {{ article.category }}
                  </el-tag>
                  <span class="news-date">{{ formatDate(article.created_at) }}</span>
                </div>
              </div>
              <div class="news-summary">
                {{ article.summary || article.content.substring(0, 150) + '...' }}
              </div>
              <div class="news-actions">
                <el-button size="small" @click="viewNews(article)">查看详情</el-button>
                <el-button size="small" type="primary" @click="editNews(article)">编辑</el-button>
                <el-button 
                  size="small" 
                  type="danger" 
                  @click="deleteNews(article)"
                >
                  删除
                </el-button>
              </div>
            </div>
          </el-card>
        </div>
      </div>

      <!-- 分页 -->
      <div class="pagination">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[5, 10, 20, 50]"
          :total="filteredNews.length"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 新增/编辑新闻对话框 -->
    <el-dialog
      v-model="showAddDialog"
      :title="editingNews ? '编辑新闻' : '发布新闻'"
      width="800px"
      @close="resetForm"
    >
      <el-form
        ref="newsFormRef"
        :model="newsForm"
        :rules="formRules"
        label-width="100px"
      >
        <el-form-item label="新闻标题" prop="title">
          <el-input v-model="newsForm.title" placeholder="请输入新闻标题" />
        </el-form-item>
        
        <el-form-item label="新闻分类" prop="category">
          <el-select v-model="newsForm.category" placeholder="请选择分类" style="width: 100%">
            <el-option label="赛事新闻" value="赛事新闻" />
            <el-option label="球员动态" value="球员动态" />
            <el-option label="青训资讯" value="青训资讯" />
            <el-option label="政策公告" value="政策公告" />
            <el-option label="其他" value="其他" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="新闻摘要" prop="summary">
          <el-input
            v-model="newsForm.summary"
            type="textarea"
            :rows="3"
            placeholder="请输入新闻摘要（可选）"
          />
        </el-form-item>
        
        <el-form-item label="新闻内容" prop="content">
          <el-input
            v-model="newsForm.content"
            type="textarea"
            :rows="8"
            placeholder="请输入新闻内容"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showAddDialog = false">取消</el-button>
          <el-button type="primary" @click="saveNews" :loading="saving">
            {{ editingNews ? '更新' : '发布' }}
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 新闻详情对话框 -->
    <el-dialog
      v-model="showDetailDialog"
      title="新闻详情"
      width="800px"
    >
      <div v-if="selectedNews" class="news-detail">
        <div class="detail-header">
          <h2 class="detail-title">{{ selectedNews.title }}</h2>
          <div class="detail-meta">
            <el-tag :type="getCategoryType(selectedNews.category)">
              {{ selectedNews.category }}
            </el-tag>
            <span class="detail-date">{{ formatDate(selectedNews.created_at) }}</span>
          </div>
        </div>
        <div class="detail-content">
          <p v-if="selectedNews.summary" class="detail-summary">{{ selectedNews.summary }}</p>
          <div class="detail-text">{{ selectedNews.content }}</div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'

export default {
  name: 'NewsPage',
  setup() {
    // 响应式数据
    const loading = ref(false)
    const saving = ref(false)
    const searchText = ref('')
    const currentPage = ref(1)
    const pageSize = ref(10)
    const showAddDialog = ref(false)
    const showDetailDialog = ref(false)
    const editingNews = ref(null)
    const selectedNews = ref(null)
    const newsFormRef = ref()
    
    // 模拟新闻数据
    const newsList = ref([
      {
        id: 1,
        title: '2024年全国青少年足球发展联赛圆满落幕',
        category: '赛事新闻',
        summary: '经过激烈角逐，长沙雅礼外国语学校U13队夺得冠军，展现了优秀的技战术水平。',
        content: '2024年全国青少年足球发展联赛于今日在沈阳奥体中心圆满落幕。本届赛事历时8个月，共有来自全国各地的156支球队参赛，参赛球员超过3000人。经过激烈的角逐，长沙雅礼外国语学校U13队以出色的表现夺得冠军，南昌洪城U13队获得亚军，北京青训中心U13队获得季军。本届赛事不仅展现了中国青少年足球的发展水平，也为优秀的青少年球员提供了展示才华的舞台。',
        created_at: '2024-08-23T18:00:00'
      },
      {
        id: 2,
        title: '李明轩：从校园足球走向职业梦想',
        category: '球员动态',
        summary: '长沙雅礼外国语学校主力前锋李明轩在决赛中打进制胜球，展现了出色的个人能力。',
        content: '13岁的李明轩是长沙雅礼外国语学校U13队的主力前锋，在刚刚结束的全国青少年足球发展联赛决赛中，他在关键时刻打进制胜球，帮助球队夺得冠军。李明轩从8岁开始接触足球，凭借出色的天赋和刻苦的训练，逐渐成长为球队的核心球员。他的梦想是将来能够进入职业俱乐部，为中国足球贡献自己的力量。',
        created_at: '2024-08-24T10:30:00'
      },
      {
        id: 3,
        title: '教育部发布青少年足球发展新政策',
        category: '政策公告',
        summary: '教育部联合体育总局发布新政策，进一步推动校园足球与青训体系的融合发展。',
        content: '教育部、国家体育总局日前联合发布《关于进一步推动青少年足球发展的指导意见》，提出要建立健全校园足球与青训体系融合发展机制，完善青少年足球竞赛体系，加强青少年足球师资队伍建设。新政策将为中国青少年足球发展提供更加有力的政策保障和制度支撑。',
        created_at: '2024-08-20T14:00:00'
      }
    ])
    
    // 表单数据
    const newsForm = reactive({
      title: '',
      category: '',
      summary: '',
      content: ''
    })
    
    // 表单验证规则
    const formRules = {
      title: [
        { required: true, message: '请输入新闻标题', trigger: 'blur' }
      ],
      category: [
        { required: true, message: '请选择新闻分类', trigger: 'change' }
      ],
      content: [
        { required: true, message: '请输入新闻内容', trigger: 'blur' }
      ]
    }
    
    // 计算属性
    const filteredNews = computed(() => {
      if (!searchText.value) return newsList.value
      
      return newsList.value.filter(news =>
        news.title.toLowerCase().includes(searchText.value.toLowerCase()) ||
        news.content.toLowerCase().includes(searchText.value.toLowerCase())
      )
    })
    
    const paginatedNews = computed(() => {
      const start = (currentPage.value - 1) * pageSize.value
      const end = start + pageSize.value
      return filteredNews.value.slice(start, end)
    })
    
    // 方法
    const getCategoryType = (category) => {
      const typeMap = {
        '赛事新闻': 'primary',
        '球员动态': 'success',
        '青训资讯': 'info',
        '政策公告': 'warning',
        '其他': ''
      }
      return typeMap[category] || ''
    }
    
    const formatDate = (dateString) => {
      if (!dateString) return ''
      const date = new Date(dateString)
      return date.toLocaleString('zh-CN')
    }
    
    const handleSearch = () => {
      currentPage.value = 1
    }
    
    const handleSizeChange = (val) => {
      pageSize.value = val
      currentPage.value = 1
    }
    
    const handleCurrentChange = (val) => {
      currentPage.value = val
    }
    
    const viewNews = (news) => {
      selectedNews.value = news
      showDetailDialog.value = true
    }
    
    const editNews = (news) => {
      editingNews.value = news
      newsForm.title = news.title
      newsForm.category = news.category
      newsForm.summary = news.summary
      newsForm.content = news.content
      showAddDialog.value = true
    }
    
    const deleteNews = async (news) => {
      try {
        await ElMessageBox.confirm(
          `确定要删除新闻"${news.title}"吗？`,
          '确认删除',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }
        )
        
        const index = newsList.value.findIndex(n => n.id === news.id)
        if (index !== -1) {
          newsList.value.splice(index, 1)
          ElMessage.success('删除成功')
        }
      } catch (error) {
        if (error !== 'cancel') {
          ElMessage.error('删除失败')
        }
      }
    }
    
    const saveNews = async () => {
      if (!newsFormRef.value) return
      
      try {
        await newsFormRef.value.validate()
        saving.value = true
        
        const formData = {
          title: newsForm.title,
          category: newsForm.category,
          summary: newsForm.summary,
          content: newsForm.content,
          created_at: new Date().toISOString()
        }
        
        if (editingNews.value) {
          // 更新
          const index = newsList.value.findIndex(n => n.id === editingNews.value.id)
          if (index !== -1) {
            newsList.value[index] = { ...newsList.value[index], ...formData }
            ElMessage.success('更新成功')
          }
        } else {
          // 新增
          const newNews = {
            id: Date.now(),
            ...formData
          }
          newsList.value.unshift(newNews)
          ElMessage.success('发布成功')
        }
        
        showAddDialog.value = false
        resetForm()
      } catch (error) {
        if (error.message) {
          ElMessage.error('保存失败')
        }
      } finally {
        saving.value = false
      }
    }
    
    const resetForm = () => {
      editingNews.value = null
      newsForm.title = ''
      newsForm.category = ''
      newsForm.summary = ''
      newsForm.content = ''
      if (newsFormRef.value) {
        newsFormRef.value.resetFields()
      }
    }
    
    // 生命周期
    onMounted(() => {
      // 这里可以加载真实的新闻数据
    })
    
    return {
      loading,
      saving,
      searchText,
      currentPage,
      pageSize,
      showAddDialog,
      showDetailDialog,
      editingNews,
      selectedNews,
      newsFormRef,
      newsList,
      newsForm,
      formRules,
      filteredNews,
      paginatedNews,
      getCategoryType,
      formatDate,
      handleSearch,
      handleSizeChange,
      handleCurrentChange,
      viewNews,
      editNews,
      deleteNews,
      saveNews,
      resetForm
    }
  }
}
</script>

<style scoped>
.news {
  min-height: 100vh;
}

.toolbar {
  margin-bottom: 20px;
}

.news-list {
  margin-bottom: 20px;
}

.news-card {
  margin-bottom: 20px;
}

.news-content {
  padding: 10px;
}

.news-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
}

.news-title {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
  margin: 0;
  flex: 1;
  margin-right: 20px;
}

.news-meta {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-shrink: 0;
}

.news-date {
  font-size: 12px;
  color: #909399;
}

.news-summary {
  color: #606266;
  line-height: 1.6;
  margin-bottom: 16px;
}

.news-actions {
  display: flex;
  gap: 8px;
}

.pagination {
  margin-top: 20px;
  text-align: right;
}

.empty-state {
  padding: 40px 0;
  text-align: center;
}

.news-detail {
  padding: 20px 0;
}

.detail-header {
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e6e6e6;
}

.detail-title {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 12px 0;
}

.detail-meta {
  display: flex;
  align-items: center;
  gap: 12px;
}

.detail-date {
  font-size: 14px;
  color: #909399;
}

.detail-content {
  line-height: 1.8;
}

.detail-summary {
  font-size: 16px;
  color: #606266;
  font-style: italic;
  margin-bottom: 20px;
  padding: 16px;
  background-color: #f8f9fa;
  border-left: 4px solid #409eff;
}

.detail-text {
  color: #303133;
  white-space: pre-wrap;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .news-header {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .news-title {
    margin-right: 0;
    margin-bottom: 8px;
  }
  
  .news-meta {
    align-self: flex-end;
  }
  
  .pagination {
    text-align: center;
  }
  
  .detail-title {
    font-size: 20px;
  }
}
</style>
