<template>
  <div class="matches">
    <div class="page-container">
      <h1 class="page-title">比赛数据</h1>
      
      <!-- 搜索和操作栏 -->
      <div class="toolbar">
        <el-row :gutter="20" justify="space-between">
          <el-col :span="16">
            <el-input
              v-model="searchText"
              placeholder="搜索比赛或球队"
              clearable
              @input="handleSearch"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
          </el-col>
          <el-col :span="8" style="text-align: right">
            <el-button type="primary" @click="showAddDialog = true">
              <el-icon><Plus /></el-icon>
              新增比赛
            </el-button>
          </el-col>
        </el-row>
      </div>

      <!-- 比赛列表 -->
      <el-table 
        :data="filteredMatches" 
        v-loading="loading"
        stripe
        style="width: 100%"
      >
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column label="主队" width="150">
          <template #default="scope">
            {{ getTeamName(scope.row.home_team_id) }}
          </template>
        </el-table-column>
        <el-table-column label="客队" width="150">
          <template #default="scope">
            {{ getTeamName(scope.row.away_team_id) }}
          </template>
        </el-table-column>
        <el-table-column label="比分" width="100" align="center">
          <template #default="scope">
            <span class="score">{{ scope.row.home_score }} : {{ scope.row.away_score }}</span>
          </template>
        </el-table-column>
        <el-table-column label="比赛时间" width="180">
          <template #default="scope">
            {{ formatDateTime(scope.row.match_date) }}
          </template>
        </el-table-column>
        <el-table-column prop="location" label="比赛地点" width="150" />
        <el-table-column label="状态" width="100">
          <template #default="scope">
            <el-tag :type="getStatusType(scope.row.status)">
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="180" fixed="right">
          <template #default="scope">
            <el-button size="small" @click="viewMatch(scope.row)">查看</el-button>
            <el-button size="small" type="primary" @click="editMatch(scope.row)">编辑</el-button>
            <el-button 
              size="small" 
              type="danger" 
              @click="deleteMatch(scope.row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="matches.length"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 新增/编辑比赛对话框 -->
    <el-dialog
      v-model="showAddDialog"
      :title="editingMatch ? '编辑比赛' : '新增比赛'"
      width="600px"
      @close="resetForm"
    >
      <el-form
        ref="matchFormRef"
        :model="matchForm"
        :rules="formRules"
        label-width="100px"
      >
        <el-form-item label="所属赛事" prop="tournament_id">
          <el-select v-model="matchForm.tournament_id" placeholder="请选择赛事" style="width: 100%">
            <el-option
              v-for="tournament in tournaments"
              :key="tournament.id"
              :label="tournament.name"
              :value="tournament.id"
            />
          </el-select>
        </el-form-item>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="主队" prop="home_team_id">
              <el-select v-model="matchForm.home_team_id" placeholder="请选择主队" style="width: 100%">
                <el-option
                  v-for="team in teams"
                  :key="team.id"
                  :label="team.name"
                  :value="team.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="客队" prop="away_team_id">
              <el-select v-model="matchForm.away_team_id" placeholder="请选择客队" style="width: 100%">
                <el-option
                  v-for="team in teams"
                  :key="team.id"
                  :label="team.name"
                  :value="team.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-form-item label="比赛时间" prop="match_date">
          <el-date-picker
            v-model="matchForm.match_date"
            type="datetime"
            placeholder="选择比赛时间"
            format="YYYY-MM-DD HH:mm"
            value-format="YYYY-MM-DD HH:mm:ss"
            style="width: 100%"
          />
        </el-form-item>
        
        <el-form-item label="比赛地点" prop="location">
          <el-input v-model="matchForm.location" placeholder="请输入比赛地点" />
        </el-form-item>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="主队比分" prop="home_score">
              <el-input-number 
                v-model="matchForm.home_score" 
                :min="0" 
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="客队比分" prop="away_score">
              <el-input-number 
                v-model="matchForm.away_score" 
                :min="0" 
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-form-item label="比赛状态" prop="status">
          <el-select v-model="matchForm.status" placeholder="请选择状态" style="width: 100%">
            <el-option label="未开始" value="scheduled" />
            <el-option label="进行中" value="in_progress" />
            <el-option label="已结束" value="completed" />
            <el-option label="已取消" value="cancelled" />
          </el-select>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showAddDialog = false">取消</el-button>
          <el-button type="primary" @click="saveMatch" :loading="saving">
            {{ editingMatch ? '更新' : '创建' }}
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 比赛详情对话框 -->
    <el-dialog
      v-model="showDetailDialog"
      title="比赛详情"
      width="800px"
    >
      <div v-if="selectedMatch" class="match-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="主队">
            {{ getTeamName(selectedMatch.home_team_id) }}
          </el-descriptions-item>
          <el-descriptions-item label="客队">
            {{ getTeamName(selectedMatch.away_team_id) }}
          </el-descriptions-item>
          <el-descriptions-item label="比分">
            {{ selectedMatch.home_score }} : {{ selectedMatch.away_score }}
          </el-descriptions-item>
          <el-descriptions-item label="比赛状态">
            <el-tag :type="getStatusType(selectedMatch.status)">
              {{ getStatusText(selectedMatch.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="比赛时间">
            {{ formatDateTime(selectedMatch.match_date) }}
          </el-descriptions-item>
          <el-descriptions-item label="比赛地点">
            {{ selectedMatch.location }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { matchAPI, teamAPI, tournamentAPI } from '@/api'
import { useDataStore } from '@/stores'

export default {
  name: 'MatchesPage',
  setup() {
    const dataStore = useDataStore()
    
    // 响应式数据
    const loading = ref(false)
    const saving = ref(false)
    const searchText = ref('')
    const currentPage = ref(1)
    const pageSize = ref(20)
    const showAddDialog = ref(false)
    const showDetailDialog = ref(false)
    const editingMatch = ref(null)
    const selectedMatch = ref(null)
    const matchFormRef = ref()
    
    const matches = computed(() => dataStore.matches)
    const teams = computed(() => dataStore.teams)
    const tournaments = computed(() => dataStore.tournaments)
    
    // 表单数据
    const matchForm = reactive({
      tournament_id: null,
      home_team_id: null,
      away_team_id: null,
      match_date: '',
      location: '',
      home_score: 0,
      away_score: 0,
      status: 'scheduled'
    })
    
    // 表单验证规则
    const formRules = {
      tournament_id: [
        { required: true, message: '请选择所属赛事', trigger: 'change' }
      ],
      home_team_id: [
        { required: true, message: '请选择主队', trigger: 'change' }
      ],
      away_team_id: [
        { required: true, message: '请选择客队', trigger: 'change' }
      ],
      match_date: [
        { required: true, message: '请选择比赛时间', trigger: 'change' }
      ]
    }
    
    // 计算属性
    const filteredMatches = computed(() => {
      if (!searchText.value) return matches.value
      
      return matches.value.filter(match => {
        const homeTeam = getTeamName(match.home_team_id)
        const awayTeam = getTeamName(match.away_team_id)
        return homeTeam.toLowerCase().includes(searchText.value.toLowerCase()) ||
               awayTeam.toLowerCase().includes(searchText.value.toLowerCase())
      })
    })
    
    // 方法
    const loadData = async () => {
      loading.value = true
      try {
        const [matchesData, teamsData, tournamentsData] = await Promise.all([
          matchAPI.getAll(),
          teamAPI.getAll(),
          tournamentAPI.getAll()
        ])
        dataStore.setMatches(matchesData)
        dataStore.setTeams(teamsData)
        dataStore.setTournaments(tournamentsData)
      } catch (error) {
        ElMessage.error('加载数据失败')
        console.error('加载数据失败:', error)
      } finally {
        loading.value = false
      }
    }
    
    const getTeamName = (teamId) => {
      const team = teams.value.find(t => t.id === teamId)
      return team ? team.name : '未知球队'
    }
    
    const getStatusType = (status) => {
      const statusMap = {
        'scheduled': '',
        'in_progress': 'warning',
        'completed': 'success',
        'cancelled': 'danger'
      }
      return statusMap[status] || ''
    }
    
    const getStatusText = (status) => {
      const statusMap = {
        'scheduled': '未开始',
        'in_progress': '进行中',
        'completed': '已结束',
        'cancelled': '已取消'
      }
      return statusMap[status] || status
    }
    
    const formatDateTime = (dateString) => {
      if (!dateString) return ''
      const date = new Date(dateString)
      return date.toLocaleString('zh-CN')
    }
    
    const handleSearch = () => {
      currentPage.value = 1
    }
    
    const handleSizeChange = (val) => {
      pageSize.value = val
      currentPage.value = 1
    }
    
    const handleCurrentChange = (val) => {
      currentPage.value = val
    }
    
    const viewMatch = (match) => {
      selectedMatch.value = match
      showDetailDialog.value = true
    }
    
    const editMatch = (match) => {
      editingMatch.value = match
      matchForm.tournament_id = match.tournament_id
      matchForm.home_team_id = match.home_team_id
      matchForm.away_team_id = match.away_team_id
      matchForm.match_date = match.match_date
      matchForm.location = match.location
      matchForm.home_score = match.home_score
      matchForm.away_score = match.away_score
      matchForm.status = match.status
      showAddDialog.value = true
    }
    
    const deleteMatch = async (match) => {
      try {
        await ElMessageBox.confirm(
          `确定要删除这场比赛吗？`,
          '确认删除',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }
        )
        
        await matchAPI.delete(match.id)
        dataStore.deleteMatch(match.id)
        ElMessage.success('删除成功')
      } catch (error) {
        if (error !== 'cancel') {
          ElMessage.error('删除失败')
          console.error('删除比赛失败:', error)
        }
      }
    }
    
    const saveMatch = async () => {
      if (!matchFormRef.value) return
      
      try {
        await matchFormRef.value.validate()
        
        if (matchForm.home_team_id === matchForm.away_team_id) {
          ElMessage.error('主队和客队不能相同')
          return
        }
        
        saving.value = true
        
        const formData = {
          tournament_id: matchForm.tournament_id,
          home_team_id: matchForm.home_team_id,
          away_team_id: matchForm.away_team_id,
          match_date: matchForm.match_date,
          location: matchForm.location,
          home_score: matchForm.home_score,
          away_score: matchForm.away_score,
          status: matchForm.status
        }
        
        if (editingMatch.value) {
          // 更新
          const updatedMatch = await matchAPI.update(editingMatch.value.id, formData)
          dataStore.updateMatch(editingMatch.value.id, updatedMatch)
          ElMessage.success('更新成功')
        } else {
          // 新增
          const newMatch = await matchAPI.create(formData)
          dataStore.addMatch(newMatch)
          ElMessage.success('创建成功')
        }
        
        showAddDialog.value = false
        resetForm()
      } catch (error) {
        if (error.message) {
          ElMessage.error('保存失败')
          console.error('保存比赛失败:', error)
        }
      } finally {
        saving.value = false
      }
    }
    
    const resetForm = () => {
      editingMatch.value = null
      matchForm.tournament_id = null
      matchForm.home_team_id = null
      matchForm.away_team_id = null
      matchForm.match_date = ''
      matchForm.location = ''
      matchForm.home_score = 0
      matchForm.away_score = 0
      matchForm.status = 'scheduled'
      if (matchFormRef.value) {
        matchFormRef.value.resetFields()
      }
    }
    
    // 生命周期
    onMounted(() => {
      loadData()
    })
    
    return {
      loading,
      saving,
      searchText,
      currentPage,
      pageSize,
      showAddDialog,
      showDetailDialog,
      editingMatch,
      selectedMatch,
      matchFormRef,
      matches,
      teams,
      tournaments,
      matchForm,
      formRules,
      filteredMatches,
      getTeamName,
      getStatusType,
      getStatusText,
      formatDateTime,
      handleSearch,
      handleSizeChange,
      handleCurrentChange,
      viewMatch,
      editMatch,
      deleteMatch,
      saveMatch,
      resetForm
    }
  }
}
</script>

<style scoped>
.matches {
  min-height: 100vh;
}

.toolbar {
  margin-bottom: 20px;
}

.pagination {
  margin-top: 20px;
  text-align: right;
}

.match-detail {
  padding: 20px 0;
}

.score {
  font-weight: 600;
  font-size: 16px;
  color: #409eff;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .toolbar .el-col {
    margin-bottom: 10px;
  }
  
  .pagination {
    text-align: center;
  }
}
</style>
