import { createRouter, createWebHistory } from 'vue-router'

const routes = [
  {
    path: '/',
    name: 'Home',
    component: () => import('@/views/Home.vue'),
    meta: { title: '首页' }
  },
  {
    path: '/tournaments',
    name: 'Tournaments',
    component: () => import('@/views/Tournaments.vue'),
    meta: { title: '赛事中心' }
  },
  {
    path: '/teams',
    name: 'Teams',
    component: () => import('@/views/Teams.vue'),
    meta: { title: '球队管理' }
  },
  {
    path: '/players',
    name: 'Players',
    component: () => import('@/views/Players.vue'),
    meta: { title: '球员档案' }
  },
  {
    path: '/matches',
    name: 'Matches',
    component: () => import('@/views/Matches.vue'),
    meta: { title: '比赛数据' }
  },
  {
    path: '/statistics',
    name: 'Statistics',
    component: () => import('@/views/Statistics.vue'),
    meta: { title: '数据统计' }
  },
  {
    path: '/news',
    name: 'News',
    component: () => import('@/views/News.vue'),
    meta: { title: '新闻动态' }
  },
  {
    path: '/admin',
    name: 'Admin',
    component: () => import('@/views/Admin.vue'),
    meta: { title: '系统管理', requiresAuth: true }
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 路由守卫
router.beforeEach((to, from, next) => {
  // 设置页面标题
  document.title = `${to.meta.title} - 中国青训足球数据平台`
  
  // 权限检查（简单示例）
  if (to.meta.requiresAuth) {
    // 这里可以添加权限验证逻辑
    // const isAuthenticated = checkAuth()
    // if (!isAuthenticated) {
    //   next('/login')
    //   return
    // }
  }
  
  next()
})

export default router
