<template>
    <div class="statistics">
        <div class="page-container">
            <h1 class="page-title">数据统计</h1>

            <!-- 统计图表 -->
            <el-row :gutter="20">
                <el-col :xs="24" :sm="12" :md="12" :lg="12">
                    <el-card>
                        <template #header>
                            <div class="card-header">
                                <span>球员进球排行榜</span>
                            </div>
                        </template>
                        <div v-if="topScorers.length === 0" class="empty-state">
                            <el-empty description="暂无数据" />
                        </div>
                        <div v-else>
                            <div v-for="(player, index) in topScorers" :key="player.id" class="ranking-item">
                                <div class="rank">{{ index + 1 }}</div>
                                <div class="player-info">
                                    <div class="name">{{ player.name }}</div>
                                    <div class="team">{{ getTeamName(player.team_id) }}</div>
                                </div>
                                <div class="goals">{{ player.goals }}球</div>
                            </div>
                        </div>
                    </el-card>
                </el-col>

                <el-col :xs="24" :sm="12" :md="12" :lg="12">
                    <el-card>
                        <template #header>
                            <div class="card-header">
                                <span>球队胜率统计</span>
                            </div>
                        </template>
                        <div v-if="teamStats.length === 0" class="empty-state">
                            <el-empty description="暂无数据" />
                        </div>
                        <div v-else>
                            <div v-for="team in teamStats" :key="team.id" class="team-stat-item">
                                <div class="team-name">{{ team.name }}</div>
                                <div class="stats">
                                    <span class="wins">胜: {{ team.wins }}</span>
                                    <span class="draws">平: {{ team.draws }}</span>
                                    <span class="losses">负: {{ team.losses }}</span>
                                    <span class="win-rate">胜率: {{ team.winRate }}%</span>
                                </div>
                            </div>
                        </div>
                    </el-card>
                </el-col>
            </el-row>

            <!-- 赛事统计 -->
            <el-row :gutter="20" style="margin-top: 20px;">
                <el-col :span="24">
                    <el-card>
                        <template #header>
                            <div class="card-header">
                                <span>赛事统计概览</span>
                            </div>
                        </template>
                        <el-row :gutter="20">
                            <el-col :xs="12" :sm="6" :md="6" :lg="6">
                                <div class="stat-box">
                                    <div class="stat-number">{{ overview.tournaments || 0 }}</div>
                                    <div class="stat-label">总赛事数</div>
                                </div>
                            </el-col>
                            <el-col :xs="12" :sm="6" :md="6" :lg="6">
                                <div class="stat-box">
                                    <div class="stat-number">{{ overview.teams || 0 }}</div>
                                    <div class="stat-label">参赛球队</div>
                                </div>
                            </el-col>
                            <el-col :xs="12" :sm="6" :md="6" :lg="6">
                                <div class="stat-box">
                                    <div class="stat-number">{{ overview.players || 0 }}</div>
                                    <div class="stat-label">注册球员</div>
                                </div>
                            </el-col>
                            <el-col :xs="12" :sm="6" :md="6" :lg="6">
                                <div class="stat-box">
                                    <div class="stat-number">{{ overview.matches || 0 }}</div>
                                    <div class="stat-label">总比赛场次</div>
                                </div>
                            </el-col>
                        </el-row>
                    </el-card>
                </el-col>
            </el-row>

            <!-- 比赛状态分布 -->
            <el-row :gutter="20" style="margin-top: 20px;">
                <el-col :span="24">
                    <el-card>
                        <template #header>
                            <div class="card-header">
                                <span>比赛状态分布</span>
                            </div>
                        </template>
                        <el-row :gutter="20">
                            <el-col :xs="12" :sm="6" :md="6" :lg="6">
                                <div class="status-box scheduled">
                                    <div class="status-number">{{ matchStatusStats.scheduled || 0 }}</div>
                                    <div class="status-label">未开始</div>
                                </div>
                            </el-col>
                            <el-col :xs="12" :sm="6" :md="6" :lg="6">
                                <div class="status-box in-progress">
                                    <div class="status-number">{{ matchStatusStats.in_progress || 0 }}</div>
                                    <div class="status-label">进行中</div>
                                </div>
                            </el-col>
                            <el-col :xs="12" :sm="6" :md="6" :lg="6">
                                <div class="status-box completed">
                                    <div class="status-number">{{ matchStatusStats.completed || 0 }}</div>
                                    <div class="status-label">已结束</div>
                                </div>
                            </el-col>
                            <el-col :xs="12" :sm="6" :md="6" :lg="6">
                                <div class="status-box cancelled">
                                    <div class="status-number">{{ matchStatusStats.cancelled || 0 }}</div>
                                    <div class="status-label">已取消</div>
                                </div>
                            </el-col>
                        </el-row>
                    </el-card>
                </el-col>
            </el-row>

            <!-- 位置分布统计 -->
            <el-row :gutter="20" style="margin-top: 20px;">
                <el-col :span="24">
                    <el-card>
                        <template #header>
                            <div class="card-header">
                                <span>球员位置分布</span>
                            </div>
                        </template>
                        <el-row :gutter="20">
                            <el-col :xs="12" :sm="6" :md="6" :lg="6" v-for="(count, position) in positionStats"
                                :key="position">
                                <div class="position-box">
                                    <div class="position-number">{{ count }}</div>
                                    <div class="position-label">{{ position }}</div>
                                </div>
                            </el-col>
                        </el-row>
                    </el-card>
                </el-col>
            </el-row>
        </div>
    </div>
</template>

<script>
    import { ref, computed, onMounted } from 'vue'
    import { ElMessage } from 'element-plus'
    import { statisticsAPI, playerAPI, teamAPI, matchAPI } from '@/api'
    import { useDataStore } from '@/stores'

    export default {
        name: 'StatisticsPage',
        setup () {
            const dataStore = useDataStore()

            // 响应式数据
            const loading = ref(false)
            const overview = ref({})
            const topScorers = ref([])
            const teamStats = ref([])
            const matchStatusStats = ref({})
            const positionStats = ref({})

            const teams = computed(() => dataStore.teams)
            const players = computed(() => dataStore.players)
            const matches = computed(() => dataStore.matches)

            // 方法
            const getTeamName = (teamId) => {
                const team = teams.value.find(t => t.id === teamId)
                return team ? team.name : '未知球队'
            }

            const loadStatistics = async () => {
                loading.value = true
                try {
                    // 加载基础数据
                    const [overviewData, playersData, teamsData, matchesData] = await Promise.all([
                        statisticsAPI.getOverview(),
                        playerAPI.getAll(),
                        teamAPI.getAll(),
                        matchAPI.getAll()
                    ])

                    overview.value = overviewData
                    dataStore.setPlayers(playersData)
                    dataStore.setTeams(teamsData)
                    dataStore.setMatches(matchesData)

                    // 计算进球排行榜
                    calculateTopScorers()

                    // 计算球队统计
                    calculateTeamStats()

                    // 计算比赛状态统计
                    calculateMatchStatusStats()

                    // 计算位置分布
                    calculatePositionStats()

                } catch (error) {
                    ElMessage.error('加载统计数据失败')
                    console.error('加载统计数据失败:', error)
                } finally {
                    loading.value = false
                }
            }

            const calculateTopScorers = () => {
                topScorers.value = players.value
                    .map(player => ({
                        ...player,
                        goals: player.metadata?.goals || 0
                    }))
                    .filter(player => player.goals > 0)
                    .sort((a, b) => b.goals - a.goals)
                    .slice(0, 10)
            }

            const calculateTeamStats = () => {
                const teamStatsMap = {}

                // 初始化球队统计
                teams.value.forEach(team => {
                    teamStatsMap[team.id] = {
                        id: team.id,
                        name: team.name,
                        wins: 0,
                        draws: 0,
                        losses: 0,
                        totalMatches: 0
                    }
                })

                // 计算比赛结果
                matches.value
                    .filter(match => match.status === 'completed')
                    .forEach(match => {
                        const homeTeam = teamStatsMap[match.home_team_id]
                        const awayTeam = teamStatsMap[match.away_team_id]

                        if (homeTeam && awayTeam) {
                            homeTeam.totalMatches++
                            awayTeam.totalMatches++

                            if (match.home_score > match.away_score) {
                                homeTeam.wins++
                                awayTeam.losses++
                            } else if (match.home_score < match.away_score) {
                                awayTeam.wins++
                                homeTeam.losses++
                            } else {
                                homeTeam.draws++
                                awayTeam.draws++
                            }
                        }
                    })

                // 计算胜率并排序
                teamStats.value = Object.values(teamStatsMap)
                    .filter(team => team.totalMatches > 0)
                    .map(team => ({
                        ...team,
                        winRate: team.totalMatches > 0 ? Math.round((team.wins / team.totalMatches) * 100) : 0
                    }))
                    .sort((a, b) => b.winRate - a.winRate)
                    .slice(0, 10)
            }

            const calculateMatchStatusStats = () => {
                const stats = {
                    scheduled: 0,
                    in_progress: 0,
                    completed: 0,
                    cancelled: 0
                }

                matches.value.forEach(match => {
                    if (Object.prototype.hasOwnProperty.call(stats, match.status)) {
                        stats[match.status]++
                    }
                })

                matchStatusStats.value = stats
            }

            const calculatePositionStats = () => {
                const stats = {}

                players.value.forEach(player => {
                    const position = player.position || '未知'
                    stats[position] = (stats[position] || 0) + 1
                })

                positionStats.value = stats
            }

            // 生命周期
            onMounted(() => {
                loadStatistics()
            })

            return {
                loading,
                overview,
                topScorers,
                teamStats,
                matchStatusStats,
                positionStats,
                getTeamName
            }
        }
    }
</script>

<style scoped>
    .statistics {
        min-height: 100vh;
    }

    .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-weight: 600;
    }

    .empty-state {
        padding: 20px 0;
    }

    .ranking-item {
        display: flex;
        align-items: center;
        padding: 12px 0;
        border-bottom: 1px solid #f0f0f0;
    }

    .ranking-item:last-child {
        border-bottom: none;
    }

    .rank {
        width: 30px;
        height: 30px;
        border-radius: 50%;
        background-color: #409eff;
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 600;
        margin-right: 12px;
    }

    .player-info {
        flex: 1;
    }

    .name {
        font-weight: 500;
        color: #303133;
    }

    .team {
        font-size: 12px;
        color: #909399;
        margin-top: 2px;
    }

    .goals {
        font-weight: 600;
        color: #409eff;
    }

    .team-stat-item {
        padding: 12px 0;
        border-bottom: 1px solid #f0f0f0;
    }

    .team-stat-item:last-child {
        border-bottom: none;
    }

    .team-name {
        font-weight: 500;
        color: #303133;
        margin-bottom: 8px;
    }

    .stats {
        display: flex;
        gap: 12px;
        font-size: 12px;
    }

    .wins {
        color: #67c23a;
    }

    .draws {
        color: #e6a23c;
    }

    .losses {
        color: #f56c6c;
    }

    .win-rate {
        color: #409eff;
        font-weight: 600;
    }

    .stat-box {
        text-align: center;
        padding: 20px;
        background-color: #f8f9fa;
        border-radius: 8px;
        margin-bottom: 20px;
    }

    .stat-number {
        font-size: 32px;
        font-weight: 600;
        color: #409eff;
        line-height: 1;
    }

    .stat-label {
        font-size: 14px;
        color: #909399;
        margin-top: 8px;
    }

    .status-box {
        text-align: center;
        padding: 20px;
        border-radius: 8px;
        margin-bottom: 20px;
    }

    .status-box.scheduled {
        background-color: #f0f9ff;
    }

    .status-box.in-progress {
        background-color: #fef3e2;
    }

    .status-box.completed {
        background-color: #f0f9f0;
    }

    .status-box.cancelled {
        background-color: #fef0f0;
    }

    .status-number {
        font-size: 28px;
        font-weight: 600;
        line-height: 1;
    }

    .status-box.scheduled .status-number {
        color: #409eff;
    }

    .status-box.in-progress .status-number {
        color: #e6a23c;
    }

    .status-box.completed .status-number {
        color: #67c23a;
    }

    .status-box.cancelled .status-number {
        color: #f56c6c;
    }

    .status-label {
        font-size: 14px;
        color: #909399;
        margin-top: 8px;
    }

    .position-box {
        text-align: center;
        padding: 20px;
        background-color: #f8f9fa;
        border-radius: 8px;
        margin-bottom: 20px;
    }

    .position-number {
        font-size: 28px;
        font-weight: 600;
        color: #606266;
        line-height: 1;
    }

    .position-label {
        font-size: 14px;
        color: #909399;
        margin-top: 8px;
    }

    /* 响应式设计 */
    @media (max-width: 768px) {
        .stats {
            flex-direction: column;
            gap: 4px;
        }

        .stat-number,
        .status-number,
        .position-number {
            font-size: 24px;
        }
    }
</style>