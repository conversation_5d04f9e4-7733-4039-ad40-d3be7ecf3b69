import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

// 主应用状态
export const useAppStore = defineStore('app', () => {
  // 状态
  const loading = ref(false)
  const user = ref(null)
  const sidebarCollapsed = ref(false)

  // 计算属性
  const isLoggedIn = computed(() => !!user.value)

  // 方法
  const setLoading = (status) => {
    loading.value = status
  }

  const setUser = (userData) => {
    user.value = userData
  }

  const toggleSidebar = () => {
    sidebarCollapsed.value = !sidebarCollapsed.value
  }

  const logout = () => {
    user.value = null
  }

  return {
    loading,
    user,
    sidebarCollapsed,
    isLoggedIn,
    setLoading,
    setUser,
    toggleSidebar,
    logout
  }
})

// 数据状态管理
export const useDataStore = defineStore('data', () => {
  // 状态
  const tournaments = ref([])
  const teams = ref([])
  const players = ref([])
  const matches = ref([])
  const statistics = ref({})

  // 方法
  const setTournaments = (data) => {
    tournaments.value = data
  }

  const setTeams = (data) => {
    teams.value = data
  }

  const setPlayers = (data) => {
    players.value = data
  }

  const setMatches = (data) => {
    matches.value = data
  }

  const setStatistics = (data) => {
    statistics.value = data
  }

  // 添加数据
  const addTournament = (tournament) => {
    tournaments.value.push(tournament)
  }

  const addTeam = (team) => {
    teams.value.push(team)
  }

  const addPlayer = (player) => {
    players.value.push(player)
  }

  const addMatch = (match) => {
    matches.value.push(match)
  }

  // 更新数据
  const updateTournament = (id, data) => {
    const index = tournaments.value.findIndex(t => t.id === id)
    if (index !== -1) {
      tournaments.value[index] = { ...tournaments.value[index], ...data }
    }
  }

  const updateTeam = (id, data) => {
    const index = teams.value.findIndex(t => t.id === id)
    if (index !== -1) {
      teams.value[index] = { ...teams.value[index], ...data }
    }
  }

  const updatePlayer = (id, data) => {
    const index = players.value.findIndex(p => p.id === id)
    if (index !== -1) {
      players.value[index] = { ...players.value[index], ...data }
    }
  }

  const updateMatch = (id, data) => {
    const index = matches.value.findIndex(m => m.id === id)
    if (index !== -1) {
      matches.value[index] = { ...matches.value[index], ...data }
    }
  }

  // 删除数据
  const deleteTournament = (id) => {
    tournaments.value = tournaments.value.filter(t => t.id !== id)
  }

  const deleteTeam = (id) => {
    teams.value = teams.value.filter(t => t.id !== id)
  }

  const deletePlayer = (id) => {
    players.value = players.value.filter(p => p.id !== id)
  }

  const deleteMatch = (id) => {
    matches.value = matches.value.filter(m => m.id !== id)
  }

  return {
    tournaments,
    teams,
    players,
    matches,
    statistics,
    setTournaments,
    setTeams,
    setPlayers,
    setMatches,
    setStatistics,
    addTournament,
    addTeam,
    addPlayer,
    addMatch,
    updateTournament,
    updateTeam,
    updatePlayer,
    updateMatch,
    deleteTournament,
    deleteTeam,
    deletePlayer,
    deleteMatch
  }
})
