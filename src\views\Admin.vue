<template>
    <div class="admin">
        <div class="page-container">
            <h1 class="page-title">系统管理</h1>

            <!-- 管理功能选项卡 -->
            <el-tabs v-model="activeTab" type="border-card">
                <!-- 用户管理 -->
                <el-tab-pane label="用户管理" name="users">
                    <div class="tab-content">
                        <div class="toolbar">
                            <el-row :gutter="20" justify="space-between">
                                <el-col :span="16">
                                    <el-input v-model="userSearchText" placeholder="搜索用户名或邮箱" clearable>
                                        <template #prefix>
                                            <el-icon>
                                                <Search />
                                            </el-icon>
                                        </template>
                                    </el-input>
                                </el-col>
                                <el-col :span="8" style="text-align: right">
                                    <el-button type="primary" @click="showUserDialog = true">
                                        <el-icon>
                                            <Plus />
                                        </el-icon>
                                        新增用户
                                    </el-button>
                                </el-col>
                            </el-row>
                        </div>

                        <el-table :data="filteredUsers" v-loading="usersLoading" stripe>
                            <el-table-column prop="id" label="ID" width="80" />
                            <el-table-column prop="username" label="用户名" width="150" />
                            <el-table-column prop="email" label="邮箱" width="200" />
                            <el-table-column label="角色" width="120">
                                <template #default="scope">
                                    <el-tag :type="getRoleType(scope.row.role)">
                                        {{ getRoleText(scope.row.role) }}
                                    </el-tag>
                                </template>
                            </el-table-column>
                            <el-table-column label="最后登录" width="180">
                                <template #default="scope">
                                    {{ formatDate(scope.row.last_login) }}
                                </template>
                            </el-table-column>
                            <el-table-column label="创建时间" width="180">
                                <template #default="scope">
                                    {{ formatDate(scope.row.created_at) }}
                                </template>
                            </el-table-column>
                            <el-table-column label="操作" width="150" fixed="right">
                                <template #default="scope">
                                    <el-button size="small" type="primary" @click="editUser(scope.row)">编辑</el-button>
                                    <el-button size="small" type="danger" @click="deleteUser(scope.row)"
                                        :disabled="scope.row.role === 'admin'">
                                        删除
                                    </el-button>
                                </template>
                            </el-table-column>
                        </el-table>
                    </div>
                </el-tab-pane>

                <!-- 数据导入 -->
                <el-tab-pane label="数据导入" name="import">
                    <div class="tab-content">
                        <el-row :gutter="20">
                            <el-col :span="12">
                                <el-card>
                                    <template #header>
                                        <div class="card-header">
                                            <span>球员数据导入</span>
                                        </div>
                                    </template>
                                    <el-upload class="upload-demo" drag action="#" :before-upload="handlePlayerImport"
                                        accept=".csv,.xlsx">
                                        <el-icon class="el-icon--upload">
                                            <UploadFilled />
                                        </el-icon>
                                        <div class="el-upload__text">
                                            将文件拖到此处，或<em>点击上传</em>
                                        </div>
                                        <template #tip>
                                            <div class="el-upload__tip">
                                                支持 CSV 和 Excel 格式，文件大小不超过 10MB
                                            </div>
                                        </template>
                                    </el-upload>
                                </el-card>
                            </el-col>

                            <el-col :span="12">
                                <el-card>
                                    <template #header>
                                        <div class="card-header">
                                            <span>比赛数据导入</span>
                                        </div>
                                    </template>
                                    <el-upload class="upload-demo" drag action="#" :before-upload="handleMatchImport"
                                        accept=".csv,.xlsx">
                                        <el-icon class="el-icon--upload">
                                            <UploadFilled />
                                        </el-icon>
                                        <div class="el-upload__text">
                                            将文件拖到此处，或<em>点击上传</em>
                                        </div>
                                        <template #tip>
                                            <div class="el-upload__tip">
                                                支持 CSV 和 Excel 格式，文件大小不超过 10MB
                                            </div>
                                        </template>
                                    </el-upload>
                                </el-card>
                            </el-col>
                        </el-row>
                    </div>
                </el-tab-pane>

                <!-- 系统设置 -->
                <el-tab-pane label="系统设置" name="settings">
                    <div class="tab-content">
                        <el-form :model="systemSettings" label-width="150px">
                            <el-form-item label="系统名称">
                                <el-input v-model="systemSettings.systemName" />
                            </el-form-item>

                            <el-form-item label="系统描述">
                                <el-input v-model="systemSettings.systemDescription" type="textarea" :rows="3" />
                            </el-form-item>

                            <el-form-item label="每页显示数量">
                                <el-input-number v-model="systemSettings.pageSize" :min="10" :max="100" />
                            </el-form-item>

                            <el-form-item label="启用邮件通知">
                                <el-switch v-model="systemSettings.emailNotification" />
                            </el-form-item>

                            <el-form-item label="数据备份频率">
                                <el-select v-model="systemSettings.backupFrequency">
                                    <el-option label="每日" value="daily" />
                                    <el-option label="每周" value="weekly" />
                                    <el-option label="每月" value="monthly" />
                                </el-select>
                            </el-form-item>

                            <el-form-item>
                                <el-button type="primary" @click="saveSettings">保存设置</el-button>
                                <el-button @click="resetSettings">重置</el-button>
                            </el-form-item>
                        </el-form>
                    </div>
                </el-tab-pane>

                <!-- 操作日志 -->
                <el-tab-pane label="操作日志" name="logs">
                    <div class="tab-content">
                        <el-table :data="operationLogs" v-loading="logsLoading" stripe>
                            <el-table-column prop="id" label="ID" width="80" />
                            <el-table-column prop="user" label="操作用户" width="120" />
                            <el-table-column prop="action" label="操作类型" width="120" />
                            <el-table-column prop="target" label="操作对象" width="150" />
                            <el-table-column prop="description" label="操作描述" min-width="200" />
                            <el-table-column label="操作时间" width="180">
                                <template #default="scope">
                                    {{ formatDate(scope.row.created_at) }}
                                </template>
                            </el-table-column>
                        </el-table>
                    </div>
                </el-tab-pane>
            </el-tabs>
        </div>

        <!-- 用户编辑对话框 -->
        <el-dialog v-model="showUserDialog" :title="editingUser ? '编辑用户' : '新增用户'" width="500px" @close="resetUserForm">
            <el-form ref="userFormRef" :model="userForm" :rules="userFormRules" label-width="100px">
                <el-form-item label="用户名" prop="username">
                    <el-input v-model="userForm.username" />
                </el-form-item>

                <el-form-item label="邮箱" prop="email">
                    <el-input v-model="userForm.email" type="email" />
                </el-form-item>

                <el-form-item label="角色" prop="role">
                    <el-select v-model="userForm.role" style="width: 100%">
                        <el-option label="管理员" value="admin" />
                        <el-option label="编辑者" value="editor" />
                        <el-option label="查看者" value="viewer" />
                    </el-select>
                </el-form-item>

                <el-form-item v-if="!editingUser" label="密码" prop="password">
                    <el-input v-model="userForm.password" type="password" />
                </el-form-item>
            </el-form>

            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="showUserDialog = false">取消</el-button>
                    <el-button type="primary" @click="saveUser" :loading="userSaving">
                        {{ editingUser ? '更新' : '创建' }}
                    </el-button>
                </span>
            </template>
        </el-dialog>
    </div>
</template>

<script>
    import { ref, reactive, computed, onMounted } from 'vue'
    import { ElMessage, ElMessageBox } from 'element-plus'
    import { userAPI } from '@/api'

    export default {
        name: 'AdminPage',
        setup () {
            // 响应式数据
            const activeTab = ref('users')
            const usersLoading = ref(false)
            const logsLoading = ref(false)
            const userSaving = ref(false)
            const userSearchText = ref('')
            const showUserDialog = ref(false)
            const editingUser = ref(null)
            const userFormRef = ref()

            // 用户数据
            const users = ref([])

            // 操作日志数据
            const operationLogs = ref([
                {
                    id: 1,
                    user: 'admin',
                    action: '创建',
                    target: '球员',
                    description: '创建球员：李明轩',
                    created_at: '2024-08-23T10:30:00'
                },
                {
                    id: 2,
                    user: 'admin',
                    action: '更新',
                    target: '比赛',
                    description: '更新比赛结果：长沙雅礼 vs 南昌洪城',
                    created_at: '2024-08-23T15:45:00'
                },
                {
                    id: 3,
                    user: 'editor',
                    action: '删除',
                    target: '新闻',
                    description: '删除新闻：测试新闻',
                    created_at: '2024-08-22T14:20:00'
                }
            ])

            // 系统设置
            const systemSettings = reactive({
                systemName: '中国青训足球数据平台',
                systemDescription: '专注于中国青少年足球发展的数据管理平台',
                pageSize: 20,
                emailNotification: true,
                backupFrequency: 'daily'
            })

            // 用户表单
            const userForm = reactive({
                username: '',
                email: '',
                role: 'viewer',
                password: ''
            })

            // 用户表单验证规则
            const userFormRules = {
                username: [
                    { required: true, message: '请输入用户名', trigger: 'blur' }
                ],
                email: [
                    { required: true, message: '请输入邮箱', trigger: 'blur' },
                    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
                ],
                role: [
                    { required: true, message: '请选择角色', trigger: 'change' }
                ],
                password: [
                    { required: true, message: '请输入密码', trigger: 'blur' },
                    { min: 6, message: '密码长度至少6位', trigger: 'blur' }
                ]
            }

            // 计算属性
            const filteredUsers = computed(() => {
                if (!userSearchText.value) return users.value

                return users.value.filter(user =>
                    user.username.toLowerCase().includes(userSearchText.value.toLowerCase()) ||
                    user.email.toLowerCase().includes(userSearchText.value.toLowerCase())
                )
            })

            // 方法
            const loadUsers = async () => {
                usersLoading.value = true
                try {
                    const data = await userAPI.getAll()
                    users.value = data
                } catch (error) {
                    ElMessage.error('加载用户列表失败')
                    console.error('加载用户失败:', error)
                } finally {
                    usersLoading.value = false
                }
            }

            const getRoleType = (role) => {
                const typeMap = {
                    'admin': 'danger',
                    'editor': 'warning',
                    'viewer': 'info'
                }
                return typeMap[role] || ''
            }

            const getRoleText = (role) => {
                const textMap = {
                    'admin': '管理员',
                    'editor': '编辑者',
                    'viewer': '查看者'
                }
                return textMap[role] || role
            }

            const formatDate = (dateString) => {
                if (!dateString) return ''
                const date = new Date(dateString)
                return date.toLocaleString('zh-CN')
            }

            const editUser = (user) => {
                editingUser.value = user
                userForm.username = user.username
                userForm.email = user.email
                userForm.role = user.role
                userForm.password = ''
                showUserDialog.value = true
            }

            const deleteUser = async (user) => {
                try {
                    await ElMessageBox.confirm(
                        `确定要删除用户"${user.username}"吗？`,
                        '确认删除',
                        {
                            confirmButtonText: '确定',
                            cancelButtonText: '取消',
                            type: 'warning'
                        }
                    )

                    await userAPI.delete(user.id)
                    await loadUsers()
                    ElMessage.success('删除成功')
                } catch (error) {
                    if (error !== 'cancel') {
                        ElMessage.error('删除失败')
                        console.error('删除用户失败:', error)
                    }
                }
            }

            const saveUser = async () => {
                if (!userFormRef.value) return

                try {
                    await userFormRef.value.validate()
                    userSaving.value = true

                    const formData = {
                        username: userForm.username,
                        email: userForm.email,
                        role: userForm.role
                    }

                    if (!editingUser.value) {
                        formData.password = userForm.password
                    }

                    if (editingUser.value) {
                        // 更新
                        await userAPI.update(editingUser.value.id, formData)
                        ElMessage.success('更新成功')
                    } else {
                        // 新增
                        await userAPI.create(formData)
                        ElMessage.success('创建成功')
                    }

                    showUserDialog.value = false
                    resetUserForm()
                    await loadUsers()
                } catch (error) {
                    if (error.message) {
                        ElMessage.error('保存失败')
                        console.error('保存用户失败:', error)
                    }
                } finally {
                    userSaving.value = false
                }
            }

            const resetUserForm = () => {
                editingUser.value = null
                userForm.username = ''
                userForm.email = ''
                userForm.role = 'viewer'
                userForm.password = ''
                if (userFormRef.value) {
                    userFormRef.value.resetFields()
                }
            }

            const handlePlayerImport = () => {
                ElMessage.info('球员数据导入功能开发中...')
                return false
            }

            const handleMatchImport = () => {
                ElMessage.info('比赛数据导入功能开发中...')
                return false
            }

            const saveSettings = () => {
                ElMessage.success('设置保存成功')
            }

            const resetSettings = () => {
                systemSettings.systemName = '中国青训足球数据平台'
                systemSettings.systemDescription = '专注于中国青少年足球发展的数据管理平台'
                systemSettings.pageSize = 20
                systemSettings.emailNotification = true
                systemSettings.backupFrequency = 'daily'
                ElMessage.info('设置已重置')
            }

            // 生命周期
            onMounted(() => {
                loadUsers()
            })

            return {
                activeTab,
                usersLoading,
                logsLoading,
                userSaving,
                userSearchText,
                showUserDialog,
                editingUser,
                userFormRef,
                users,
                operationLogs,
                systemSettings,
                userForm,
                userFormRules,
                filteredUsers,
                getRoleType,
                getRoleText,
                formatDate,
                editUser,
                deleteUser,
                saveUser,
                resetUserForm,
                handlePlayerImport,
                handleMatchImport,
                saveSettings,
                resetSettings
            }
        }
    }
</script>

<style scoped>
    .admin {
        min-height: 100vh;
    }

    .tab-content {
        padding: 20px 0;
    }

    .toolbar {
        margin-bottom: 20px;
    }

    .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-weight: 600;
    }

    .upload-demo {
        width: 100%;
    }

    .dialog-footer {
        display: flex;
        justify-content: flex-end;
        gap: 10px;
    }

    /* 响应式设计 */
    @media (max-width: 768px) {
        .toolbar .el-col {
            margin-bottom: 10px;
        }
    }
</style>