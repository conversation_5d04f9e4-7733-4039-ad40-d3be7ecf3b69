// 模拟数据服务 - 临时使用，后续会连接真实数据库

// 模拟赛事数据
export const mockTournaments = [
  {
    id: 1,
    name: "2024年全国青少年足球发展联赛",
    start_date: "2024-03-01",
    end_date: "2024-11-30",
    location: "全国各地",
    description: "面向全国青少年的综合性足球发展联赛，旨在推动中国青少年足球事业发展"
  },
  {
    id: 5,
    name: "2024年第三届中国青少年足球联赛U13组",
    start_date: "2024-06-01",
    end_date: "2024-08-23",
    location: "全国各地",
    description: "由教育部、国家体育总局、中国足协联合主办的全国性青少年足球赛事"
  }
]

// 模拟球队数据
export const mockTeams = [
  {
    id: 18,
    name: "长沙雅礼外国语学校U13",
    tournament_id: 5,
    foundation_year: 2010,
    description: "2024年第三届中国青少年足球联赛U13组冠军，湖南省校园足球代表队"
  },
  {
    id: 19,
    name: "南昌洪城U13",
    tournament_id: 5,
    foundation_year: 2015,
    description: "2024年第三届中国青少年足球联赛U13组亚军，江西省青训代表队"
  },
  {
    id: 20,
    name: "北京青训中心U13",
    tournament_id: 5,
    foundation_year: 2012,
    description: "北京市足协青训中心梯队，在全国赛事中表现优异"
  }
]

// 模拟球员数据
export const mockPlayers = [
  {
    id: 24,
    name: "李明轩",
    team_id: 18,
    position: "前锋",
    birth_date: "2011-03-15",
    height: 165,
    weight: 52,
    description: "长沙雅礼外国语学校主力前锋，2024年中青赛决赛进球功臣",
    metadata: { goals: 12, assists: 5, jersey_number: 9 }
  },
  {
    id: 25,
    name: "王子豪",
    team_id: 18,
    position: "中场",
    birth_date: "2011-07-22",
    height: 162,
    weight: 48,
    description: "球队队长，中场核心，组织能力出色",
    metadata: { goals: 8, assists: 15, jersey_number: 10, captain: true }
  },
  {
    id: 28,
    name: "陈俊杰",
    team_id: 19,
    position: "前锋",
    birth_date: "2011-05-12",
    height: 163,
    weight: 50,
    description: "南昌洪城主力射手，速度快，射门精准",
    metadata: { goals: 10, assists: 4, jersey_number: 11 }
  }
]

// 模拟比赛数据
export const mockMatches = [
  {
    id: 115,
    tournament_id: 5,
    home_team_id: 18,
    away_team_id: 19,
    match_date: "2024-08-23T15:00:00",
    home_score: 3,
    away_score: 1,
    location: "沈阳奥体中心",
    status: "completed"
  },
  {
    id: 116,
    tournament_id: 5,
    home_team_id: 18,
    away_team_id: 20,
    match_date: "2024-08-20T14:30:00",
    home_score: 2,
    away_score: 1,
    location: "沈阳奥体中心",
    status: "completed"
  },
  {
    id: 117,
    tournament_id: 5,
    home_team_id: 20,
    away_team_id: 19,
    match_date: "2024-12-15T15:00:00",
    home_score: 0,
    away_score: 0,
    location: "北京工人体育场",
    status: "scheduled"
  }
]

// 模拟统计数据
export const mockStatistics = {
  tournaments: 8,
  teams: 24,
  players: 156,
  matches: 89
}

// 模拟API响应
export const mockAPI = {
  tournaments: {
    getAll: () => Promise.resolve(mockTournaments),
    getById: (id) => Promise.resolve(mockTournaments.find(t => t.id === parseInt(id))),
    create: (data) => {
      const newTournament = { ...data, id: Date.now() }
      mockTournaments.push(newTournament)
      return Promise.resolve(newTournament)
    },
    update: (id, data) => {
      const index = mockTournaments.findIndex(t => t.id === parseInt(id))
      if (index !== -1) {
        mockTournaments[index] = { ...mockTournaments[index], ...data }
        return Promise.resolve(mockTournaments[index])
      }
      return Promise.reject(new Error('Tournament not found'))
    },
    delete: (id) => {
      const index = mockTournaments.findIndex(t => t.id === parseInt(id))
      if (index !== -1) {
        mockTournaments.splice(index, 1)
        return Promise.resolve()
      }
      return Promise.reject(new Error('Tournament not found'))
    }
  },
  
  teams: {
    getAll: () => Promise.resolve(mockTeams),
    getById: (id) => Promise.resolve(mockTeams.find(t => t.id === parseInt(id))),
    getByTournament: (tournamentId) => Promise.resolve(mockTeams.filter(t => t.tournament_id === parseInt(tournamentId))),
    create: (data) => {
      const newTeam = { ...data, id: Date.now() }
      mockTeams.push(newTeam)
      return Promise.resolve(newTeam)
    },
    update: (id, data) => {
      const index = mockTeams.findIndex(t => t.id === parseInt(id))
      if (index !== -1) {
        mockTeams[index] = { ...mockTeams[index], ...data }
        return Promise.resolve(mockTeams[index])
      }
      return Promise.reject(new Error('Team not found'))
    },
    delete: (id) => {
      const index = mockTeams.findIndex(t => t.id === parseInt(id))
      if (index !== -1) {
        mockTeams.splice(index, 1)
        return Promise.resolve()
      }
      return Promise.reject(new Error('Team not found'))
    }
  },
  
  players: {
    getAll: () => Promise.resolve(mockPlayers),
    getById: (id) => Promise.resolve(mockPlayers.find(p => p.id === parseInt(id))),
    getByTeam: (teamId) => Promise.resolve(mockPlayers.filter(p => p.team_id === parseInt(teamId))),
    create: (data) => {
      const newPlayer = { ...data, id: Date.now() }
      mockPlayers.push(newPlayer)
      return Promise.resolve(newPlayer)
    },
    update: (id, data) => {
      const index = mockPlayers.findIndex(p => p.id === parseInt(id))
      if (index !== -1) {
        mockPlayers[index] = { ...mockPlayers[index], ...data }
        return Promise.resolve(mockPlayers[index])
      }
      return Promise.reject(new Error('Player not found'))
    },
    delete: (id) => {
      const index = mockPlayers.findIndex(p => p.id === parseInt(id))
      if (index !== -1) {
        mockPlayers.splice(index, 1)
        return Promise.resolve()
      }
      return Promise.reject(new Error('Player not found'))
    }
  },
  
  matches: {
    getAll: () => Promise.resolve(mockMatches),
    getById: (id) => Promise.resolve(mockMatches.find(m => m.id === parseInt(id))),
    getByTournament: (tournamentId) => Promise.resolve(mockMatches.filter(m => m.tournament_id === parseInt(tournamentId))),
    create: (data) => {
      const newMatch = { ...data, id: Date.now() }
      mockMatches.push(newMatch)
      return Promise.resolve(newMatch)
    },
    update: (id, data) => {
      const index = mockMatches.findIndex(m => m.id === parseInt(id))
      if (index !== -1) {
        mockMatches[index] = { ...mockMatches[index], ...data }
        return Promise.resolve(mockMatches[index])
      }
      return Promise.reject(new Error('Match not found'))
    },
    delete: (id) => {
      const index = mockMatches.findIndex(m => m.id === parseInt(id))
      if (index !== -1) {
        mockMatches.splice(index, 1)
        return Promise.resolve()
      }
      return Promise.reject(new Error('Match not found'))
    }
  },
  
  statistics: {
    getOverview: () => Promise.resolve(mockStatistics)
  }
}
