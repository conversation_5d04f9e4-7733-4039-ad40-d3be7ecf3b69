<template>
  <div class="teams">
    <div class="page-container">
      <h1 class="page-title">球队管理</h1>
      
      <!-- 搜索和操作栏 -->
      <div class="toolbar">
        <el-row :gutter="20" justify="space-between">
          <el-col :span="16">
            <el-input
              v-model="searchText"
              placeholder="搜索球队名称"
              clearable
              @input="handleSearch"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
          </el-col>
          <el-col :span="8" style="text-align: right">
            <el-button type="primary" @click="showAddDialog = true">
              <el-icon><Plus /></el-icon>
              新增球队
            </el-button>
          </el-col>
        </el-row>
      </div>

      <!-- 球队列表 -->
      <el-table 
        :data="filteredTeams" 
        v-loading="loading"
        stripe
        style="width: 100%"
      >
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="name" label="球队名称" min-width="200" />
        <el-table-column label="所属赛事" width="200">
          <template #default="scope">
            {{ getTournamentName(scope.row.tournament_id) }}
          </template>
        </el-table-column>
        <el-table-column prop="foundation_year" label="成立年份" width="120" />
        <el-table-column prop="description" label="描述" min-width="250" show-overflow-tooltip />
        <el-table-column label="操作" width="180" fixed="right">
          <template #default="scope">
            <el-button size="small" @click="viewTeam(scope.row)">查看</el-button>
            <el-button size="small" type="primary" @click="editTeam(scope.row)">编辑</el-button>
            <el-button 
              size="small" 
              type="danger" 
              @click="deleteTeam(scope.row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="teams.length"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 新增/编辑球队对话框 -->
    <el-dialog
      v-model="showAddDialog"
      :title="editingTeam ? '编辑球队' : '新增球队'"
      width="600px"
      @close="resetForm"
    >
      <el-form
        ref="teamFormRef"
        :model="teamForm"
        :rules="formRules"
        label-width="100px"
      >
        <el-form-item label="球队名称" prop="name">
          <el-input v-model="teamForm.name" placeholder="请输入球队名称" />
        </el-form-item>
        
        <el-form-item label="所属赛事" prop="tournament_id">
          <el-select v-model="teamForm.tournament_id" placeholder="请选择赛事" style="width: 100%">
            <el-option
              v-for="tournament in tournaments"
              :key="tournament.id"
              :label="tournament.name"
              :value="tournament.id"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="成立年份" prop="foundation_year">
          <el-input-number 
            v-model="teamForm.foundation_year" 
            :min="1900" 
            :max="new Date().getFullYear()"
            style="width: 100%"
          />
        </el-form-item>
        
        <el-form-item label="球队描述" prop="description">
          <el-input
            v-model="teamForm.description"
            type="textarea"
            :rows="4"
            placeholder="请输入球队描述"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showAddDialog = false">取消</el-button>
          <el-button type="primary" @click="saveTeam" :loading="saving">
            {{ editingTeam ? '更新' : '创建' }}
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 球队详情对话框 -->
    <el-dialog
      v-model="showDetailDialog"
      title="球队详情"
      width="800px"
    >
      <div v-if="selectedTeam" class="team-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="球队名称">
            {{ selectedTeam.name }}
          </el-descriptions-item>
          <el-descriptions-item label="所属赛事">
            {{ getTournamentName(selectedTeam.tournament_id) }}
          </el-descriptions-item>
          <el-descriptions-item label="成立年份">
            {{ selectedTeam.foundation_year }}
          </el-descriptions-item>
          <el-descriptions-item label="创建时间">
            {{ formatDate(selectedTeam.created_at) }}
          </el-descriptions-item>
          <el-descriptions-item label="球队描述" :span="2">
            {{ selectedTeam.description }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { teamAPI, tournamentAPI } from '@/api'
import { useDataStore } from '@/stores'

export default {
  name: 'TeamsPage',
  setup() {
    const dataStore = useDataStore()
    
    // 响应式数据
    const loading = ref(false)
    const saving = ref(false)
    const searchText = ref('')
    const currentPage = ref(1)
    const pageSize = ref(20)
    const showAddDialog = ref(false)
    const showDetailDialog = ref(false)
    const editingTeam = ref(null)
    const selectedTeam = ref(null)
    const teamFormRef = ref()
    
    const teams = computed(() => dataStore.teams)
    const tournaments = computed(() => dataStore.tournaments)
    
    // 表单数据
    const teamForm = reactive({
      name: '',
      tournament_id: null,
      foundation_year: new Date().getFullYear(),
      description: ''
    })
    
    // 表单验证规则
    const formRules = {
      name: [
        { required: true, message: '请输入球队名称', trigger: 'blur' }
      ],
      tournament_id: [
        { required: true, message: '请选择所属赛事', trigger: 'change' }
      ]
    }
    
    // 计算属性
    const filteredTeams = computed(() => {
      if (!searchText.value) return teams.value
      
      return teams.value.filter(team =>
        team.name.toLowerCase().includes(searchText.value.toLowerCase())
      )
    })
    
    // 方法
    const loadData = async () => {
      loading.value = true
      try {
        const [teamsData, tournamentsData] = await Promise.all([
          teamAPI.getAll(),
          tournamentAPI.getAll()
        ])
        dataStore.setTeams(teamsData)
        dataStore.setTournaments(tournamentsData)
      } catch (error) {
        ElMessage.error('加载数据失败')
        console.error('加载数据失败:', error)
      } finally {
        loading.value = false
      }
    }
    
    const getTournamentName = (tournamentId) => {
      const tournament = tournaments.value.find(t => t.id === tournamentId)
      return tournament ? tournament.name : '未知赛事'
    }
    
    const handleSearch = () => {
      currentPage.value = 1
    }
    
    const handleSizeChange = (val) => {
      pageSize.value = val
      currentPage.value = 1
    }
    
    const handleCurrentChange = (val) => {
      currentPage.value = val
    }
    
    const viewTeam = (team) => {
      selectedTeam.value = team
      showDetailDialog.value = true
    }
    
    const editTeam = (team) => {
      editingTeam.value = team
      teamForm.name = team.name
      teamForm.tournament_id = team.tournament_id
      teamForm.foundation_year = team.foundation_year
      teamForm.description = team.description
      showAddDialog.value = true
    }
    
    const deleteTeam = async (team) => {
      try {
        await ElMessageBox.confirm(
          `确定要删除球队"${team.name}"吗？`,
          '确认删除',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }
        )
        
        await teamAPI.delete(team.id)
        dataStore.deleteTeam(team.id)
        ElMessage.success('删除成功')
      } catch (error) {
        if (error !== 'cancel') {
          ElMessage.error('删除失败')
          console.error('删除球队失败:', error)
        }
      }
    }
    
    const saveTeam = async () => {
      if (!teamFormRef.value) return
      
      try {
        await teamFormRef.value.validate()
        saving.value = true
        
        const formData = {
          name: teamForm.name,
          tournament_id: teamForm.tournament_id,
          foundation_year: teamForm.foundation_year,
          description: teamForm.description
        }
        
        if (editingTeam.value) {
          // 更新
          const updatedTeam = await teamAPI.update(editingTeam.value.id, formData)
          dataStore.updateTeam(editingTeam.value.id, updatedTeam)
          ElMessage.success('更新成功')
        } else {
          // 新增
          const newTeam = await teamAPI.create(formData)
          dataStore.addTeam(newTeam)
          ElMessage.success('创建成功')
        }
        
        showAddDialog.value = false
        resetForm()
      } catch (error) {
        if (error.message) {
          ElMessage.error('保存失败')
          console.error('保存球队失败:', error)
        }
      } finally {
        saving.value = false
      }
    }
    
    const resetForm = () => {
      editingTeam.value = null
      teamForm.name = ''
      teamForm.tournament_id = null
      teamForm.foundation_year = new Date().getFullYear()
      teamForm.description = ''
      if (teamFormRef.value) {
        teamFormRef.value.resetFields()
      }
    }
    
    const formatDate = (dateString) => {
      if (!dateString) return ''
      const date = new Date(dateString)
      return date.toLocaleDateString('zh-CN')
    }
    
    // 生命周期
    onMounted(() => {
      loadData()
    })
    
    return {
      loading,
      saving,
      searchText,
      currentPage,
      pageSize,
      showAddDialog,
      showDetailDialog,
      editingTeam,
      selectedTeam,
      teamFormRef,
      teams,
      tournaments,
      teamForm,
      formRules,
      filteredTeams,
      getTournamentName,
      handleSearch,
      handleSizeChange,
      handleCurrentChange,
      viewTeam,
      editTeam,
      deleteTeam,
      saveTeam,
      resetForm,
      formatDate
    }
  }
}
</script>

<style scoped>
.teams {
  min-height: 100vh;
}

.toolbar {
  margin-bottom: 20px;
}

.pagination {
  margin-top: 20px;
  text-align: right;
}

.team-detail {
  padding: 20px 0;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .toolbar .el-col {
    margin-bottom: 10px;
  }
  
  .pagination {
    text-align: center;
  }
}
</style>
