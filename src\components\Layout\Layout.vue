<template>
    <el-container class="layout-container">
        <!-- 顶部导航栏 -->
        <el-header class="layout-header">
            <div class="header-left">
                <el-button type="text" @click="toggleSidebar" class="sidebar-toggle">
                    <el-icon>
                        <Menu />
                    </el-icon>
                </el-button>
                <h1 class="site-title">中国青训足球数据平台</h1>
            </div>
            <div class="header-right">
                <el-dropdown @command="handleCommand">
                    <span class="user-dropdown">
                        <el-icon>
                            <User />
                        </el-icon>
                        <span>{{ user?.username || '游客' }}</span>
                        <el-icon>
                            <ArrowDown />
                        </el-icon>
                    </span>
                    <template #dropdown>
                        <el-dropdown-menu>
                            <el-dropdown-item command="profile">个人资料</el-dropdown-item>
                            <el-dropdown-item command="settings">设置</el-dropdown-item>
                            <el-dropdown-item divided command="logout">退出登录</el-dropdown-item>
                        </el-dropdown-menu>
                    </template>
                </el-dropdown>
            </div>
        </el-header>

        <el-container>
            <!-- 侧边栏 -->
            <el-aside :width="sidebarCollapsed ? '64px' : '200px'" class="layout-sidebar">
                <el-menu :default-active="$route.path" :collapse="sidebarCollapsed" :unique-opened="true" router
                    class="sidebar-menu">
                    <el-menu-item index="/">
                        <el-icon>
                            <House />
                        </el-icon>
                        <template #title>首页</template>
                    </el-menu-item>

                    <el-menu-item index="/tournaments">
                        <el-icon>
                            <Trophy />
                        </el-icon>
                        <template #title>赛事中心</template>
                    </el-menu-item>

                    <el-menu-item index="/teams">
                        <el-icon>
                            <UserFilled />
                        </el-icon>
                        <template #title>球队管理</template>
                    </el-menu-item>

                    <el-menu-item index="/players">
                        <el-icon>
                            <Avatar />
                        </el-icon>
                        <template #title>球员档案</template>
                    </el-menu-item>

                    <el-menu-item index="/matches">
                        <el-icon>
                            <Football />
                        </el-icon>
                        <template #title>比赛数据</template>
                    </el-menu-item>

                    <el-menu-item index="/statistics">
                        <el-icon>
                            <DataAnalysis />
                        </el-icon>
                        <template #title>数据统计</template>
                    </el-menu-item>

                    <el-menu-item index="/news">
                        <el-icon>
                            <Document />
                        </el-icon>
                        <template #title>新闻动态</template>
                    </el-menu-item>

                    <el-menu-item index="/admin">
                        <el-icon>
                            <Setting />
                        </el-icon>
                        <template #title>系统管理</template>
                    </el-menu-item>
                </el-menu>
            </el-aside>

            <!-- 主内容区域 -->
            <el-main class="layout-main">
                <router-view />
            </el-main>
        </el-container>
    </el-container>
</template>

<script>
    import { computed } from 'vue'
    import { useAppStore } from '@/stores'
    import { ElMessage } from 'element-plus'

    export default {
        name: 'MainLayout',
        setup () {
            const appStore = useAppStore()

            const sidebarCollapsed = computed(() => appStore.sidebarCollapsed)
            const user = computed(() => appStore.user)

            const toggleSidebar = () => {
                appStore.toggleSidebar()
            }

            const handleCommand = (command) => {
                switch (command) {
                    case 'profile':
                        ElMessage.info('个人资料功能开发中...')
                        break
                    case 'settings':
                        ElMessage.info('设置功能开发中...')
                        break
                    case 'logout':
                        appStore.logout()
                        ElMessage.success('已退出登录')
                        break
                }
            }

            return {
                sidebarCollapsed,
                user,
                toggleSidebar,
                handleCommand
            }
        }
    }
</script>

<style scoped>
    .layout-container {
        height: 100vh;
    }

    .layout-header {
        background-color: #fff;
        border-bottom: 1px solid #e6e6e6;
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0 20px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .header-left {
        display: flex;
        align-items: center;
    }

    .sidebar-toggle {
        margin-right: 16px;
        font-size: 18px;
    }

    .site-title {
        font-size: 20px;
        font-weight: 600;
        color: #409eff;
        margin: 0;
    }

    .header-right {
        display: flex;
        align-items: center;
    }

    .user-dropdown {
        display: flex;
        align-items: center;
        cursor: pointer;
        padding: 8px 12px;
        border-radius: 4px;
        transition: background-color 0.3s;
    }

    .user-dropdown:hover {
        background-color: #f5f7fa;
    }

    .user-dropdown .el-icon {
        margin: 0 4px;
    }

    .layout-sidebar {
        background-color: #fff;
        border-right: 1px solid #e6e6e6;
        transition: width 0.3s;
    }

    .sidebar-menu {
        border-right: none;
        height: 100%;
    }

    .layout-main {
        background-color: #f5f5f5;
        padding: 0;
        overflow-y: auto;
    }

    /* 响应式设计 */
    @media (max-width: 768px) {
        .site-title {
            font-size: 16px;
        }

        .layout-header {
            padding: 0 10px;
        }
    }
</style>