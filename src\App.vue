<template>
    <div id="app">
        <Layout />
    </div>
</template>

<script>
    import Layout from '@/components/Layout/Layout.vue'

    export default {
        name: 'App',
        components: {
            Layout
        }
    }
</script>

<style>
    * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
    }

    #app {
        font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        height: 100vh;
        background-color: #f5f5f5;
    }

    body {
        margin: 0;
        padding: 0;
    }

    /* 全局样式 */
    .page-container {
        padding: 20px;
        background-color: #fff;
        margin: 20px;
        border-radius: 8px;
        box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    }

    .page-title {
        font-size: 24px;
        font-weight: 600;
        color: #303133;
        margin-bottom: 20px;
        padding-bottom: 12px;
        border-bottom: 2px solid #409eff;
    }

    /* 响应式设计 */
    @media (max-width: 768px) {
        .page-container {
            margin: 10px;
            padding: 15px;
        }

        .page-title {
            font-size: 20px;
        }
    }
</style>