<template>
    <div class="tournaments">
        <div class="page-container">
            <h1 class="page-title">赛事中心</h1>

            <!-- 搜索和操作栏 -->
            <div class="toolbar">
                <el-row :gutter="20" justify="space-between">
                    <el-col :span="16">
                        <el-input v-model="searchText" placeholder="搜索赛事名称或地点" clearable @input="handleSearch">
                            <template #prefix>
                                <el-icon>
                                    <Search />
                                </el-icon>
                            </template>
                        </el-input>
                    </el-col>
                    <el-col :span="8" style="text-align: right">
                        <el-button type="primary" @click="showAddDialog = true">
                            <el-icon>
                                <Plus />
                            </el-icon>
                            新增赛事
                        </el-button>
                    </el-col>
                </el-row>
            </div>

            <!-- 赛事列表 -->
            <el-table :data="filteredTournaments" v-loading="loading" stripe style="width: 100%">
                <el-table-column prop="id" label="ID" width="80" />
                <el-table-column prop="name" label="赛事名称" min-width="200" />
                <el-table-column prop="location" label="举办地点" width="150" />
                <el-table-column label="比赛时间" width="200">
                    <template #default="scope">
                        {{ formatDateRange(scope.row.start_date, scope.row.end_date) }}
                    </template>
                </el-table-column>
                <el-table-column prop="description" label="描述" min-width="250" show-overflow-tooltip />
                <el-table-column label="操作" width="180" fixed="right">
                    <template #default="scope">
                        <el-button size="small" @click="viewTournament(scope.row)">查看</el-button>
                        <el-button size="small" type="primary" @click="editTournament(scope.row)">编辑</el-button>
                        <el-button size="small" type="danger" @click="deleteTournament(scope.row)">
                            删除
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>

            <!-- 分页 -->
            <div class="pagination">
                <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize"
                    :page-sizes="[10, 20, 50, 100]" :total="tournaments.length"
                    layout="total, sizes, prev, pager, next, jumper" @size-change="handleSizeChange"
                    @current-change="handleCurrentChange" />
            </div>
        </div>

        <!-- 新增/编辑赛事对话框 -->
        <el-dialog v-model="showAddDialog" :title="editingTournament ? '编辑赛事' : '新增赛事'" width="600px"
            @close="resetForm">
            <el-form ref="tournamentFormRef" :model="tournamentForm" :rules="formRules" label-width="100px">
                <el-form-item label="赛事名称" prop="name">
                    <el-input v-model="tournamentForm.name" placeholder="请输入赛事名称" />
                </el-form-item>

                <el-form-item label="举办地点" prop="location">
                    <el-input v-model="tournamentForm.location" placeholder="请输入举办地点" />
                </el-form-item>

                <el-form-item label="比赛时间" prop="dateRange">
                    <el-date-picker v-model="tournamentForm.dateRange" type="daterange" range-separator="至"
                        start-placeholder="开始日期" end-placeholder="结束日期" format="YYYY-MM-DD" value-format="YYYY-MM-DD"
                        style="width: 100%" />
                </el-form-item>

                <el-form-item label="赛事描述" prop="description">
                    <el-input v-model="tournamentForm.description" type="textarea" :rows="4" placeholder="请输入赛事描述" />
                </el-form-item>
            </el-form>

            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="showAddDialog = false">取消</el-button>
                    <el-button type="primary" @click="saveTournament" :loading="saving">
                        {{ editingTournament ? '更新' : '创建' }}
                    </el-button>
                </span>
            </template>
        </el-dialog>

        <!-- 赛事详情对话框 -->
        <el-dialog v-model="showDetailDialog" title="赛事详情" width="800px">
            <div v-if="selectedTournament" class="tournament-detail">
                <el-descriptions :column="2" border>
                    <el-descriptions-item label="赛事名称">
                        {{ selectedTournament.name }}
                    </el-descriptions-item>
                    <el-descriptions-item label="举办地点">
                        {{ selectedTournament.location }}
                    </el-descriptions-item>
                    <el-descriptions-item label="开始时间">
                        {{ formatDate(selectedTournament.start_date) }}
                    </el-descriptions-item>
                    <el-descriptions-item label="结束时间">
                        {{ formatDate(selectedTournament.end_date) }}
                    </el-descriptions-item>
                    <el-descriptions-item label="创建时间" :span="2">
                        {{ formatDate(selectedTournament.created_at) }}
                    </el-descriptions-item>
                    <el-descriptions-item label="赛事描述" :span="2">
                        {{ selectedTournament.description }}
                    </el-descriptions-item>
                </el-descriptions>
            </div>
        </el-dialog>
    </div>
</template>

<script>
    import { ref, reactive, computed, onMounted } from 'vue'
    import { ElMessage, ElMessageBox } from 'element-plus'
    import { tournamentAPI } from '@/api'
    import { useDataStore } from '@/stores'

    export default {
        name: 'TournamentsPage',
        setup () {
            const dataStore = useDataStore()

            // 响应式数据
            const loading = ref(false)
            const saving = ref(false)
            const searchText = ref('')
            const currentPage = ref(1)
            const pageSize = ref(20)
            const showAddDialog = ref(false)
            const showDetailDialog = ref(false)
            const editingTournament = ref(null)
            const selectedTournament = ref(null)
            const tournamentFormRef = ref()

            const tournaments = computed(() => dataStore.tournaments)

            // 表单数据
            const tournamentForm = reactive({
                name: '',
                location: '',
                dateRange: [],
                description: ''
            })

            // 表单验证规则
            const formRules = {
                name: [
                    { required: true, message: '请输入赛事名称', trigger: 'blur' }
                ],
                location: [
                    { required: true, message: '请输入举办地点', trigger: 'blur' }
                ],
                dateRange: [
                    { required: true, message: '请选择比赛时间', trigger: 'change' }
                ]
            }

            // 计算属性
            const filteredTournaments = computed(() => {
                if (!searchText.value) return tournaments.value

                return tournaments.value.filter(tournament =>
                    tournament.name.toLowerCase().includes(searchText.value.toLowerCase()) ||
                    tournament.location.toLowerCase().includes(searchText.value.toLowerCase())
                )
            })

            // 方法
            const loadTournaments = async () => {
                loading.value = true
                try {
                    const data = await tournamentAPI.getAll()
                    dataStore.setTournaments(data)
                } catch (error) {
                    ElMessage.error('加载赛事列表失败')
                    console.error('加载赛事失败:', error)
                } finally {
                    loading.value = false
                }
            }

            const handleSearch = () => {
                currentPage.value = 1
            }

            const handleSizeChange = (val) => {
                pageSize.value = val
                currentPage.value = 1
            }

            const handleCurrentChange = (val) => {
                currentPage.value = val
            }

            const viewTournament = (tournament) => {
                selectedTournament.value = tournament
                showDetailDialog.value = true
            }

            const editTournament = (tournament) => {
                editingTournament.value = tournament
                tournamentForm.name = tournament.name
                tournamentForm.location = tournament.location
                tournamentForm.dateRange = [tournament.start_date, tournament.end_date]
                tournamentForm.description = tournament.description
                showAddDialog.value = true
            }

            const deleteTournament = async (tournament) => {
                try {
                    await ElMessageBox.confirm(
                        `确定要删除赛事"${tournament.name}"吗？`,
                        '确认删除',
                        {
                            confirmButtonText: '确定',
                            cancelButtonText: '取消',
                            type: 'warning'
                        }
                    )

                    await tournamentAPI.delete(tournament.id)
                    dataStore.deleteTournament(tournament.id)
                    ElMessage.success('删除成功')
                } catch (error) {
                    if (error !== 'cancel') {
                        ElMessage.error('删除失败')
                        console.error('删除赛事失败:', error)
                    }
                }
            }

            const saveTournament = async () => {
                if (!tournamentFormRef.value) return

                try {
                    await tournamentFormRef.value.validate()
                    saving.value = true

                    const formData = {
                        name: tournamentForm.name,
                        location: tournamentForm.location,
                        start_date: tournamentForm.dateRange[0],
                        end_date: tournamentForm.dateRange[1],
                        description: tournamentForm.description
                    }

                    if (editingTournament.value) {
                        // 更新
                        const updatedTournament = await tournamentAPI.update(editingTournament.value.id, formData)
                        dataStore.updateTournament(editingTournament.value.id, updatedTournament)
                        ElMessage.success('更新成功')
                    } else {
                        // 新增
                        const newTournament = await tournamentAPI.create(formData)
                        dataStore.addTournament(newTournament)
                        ElMessage.success('创建成功')
                    }

                    showAddDialog.value = false
                    resetForm()
                } catch (error) {
                    if (error.message) {
                        ElMessage.error('保存失败')
                        console.error('保存赛事失败:', error)
                    }
                } finally {
                    saving.value = false
                }
            }

            const resetForm = () => {
                editingTournament.value = null
                tournamentForm.name = ''
                tournamentForm.location = ''
                tournamentForm.dateRange = []
                tournamentForm.description = ''
                if (tournamentFormRef.value) {
                    tournamentFormRef.value.resetFields()
                }
            }

            const formatDate = (dateString) => {
                if (!dateString) return ''
                const date = new Date(dateString)
                return date.toLocaleDateString('zh-CN')
            }

            const formatDateRange = (startDate, endDate) => {
                return `${formatDate(startDate)} - ${formatDate(endDate)}`
            }

            // 生命周期
            onMounted(() => {
                loadTournaments()
            })

            return {
                loading,
                saving,
                searchText,
                currentPage,
                pageSize,
                showAddDialog,
                showDetailDialog,
                editingTournament,
                selectedTournament,
                tournamentFormRef,
                tournaments,
                tournamentForm,
                formRules,
                filteredTournaments,
                handleSearch,
                handleSizeChange,
                handleCurrentChange,
                viewTournament,
                editTournament,
                deleteTournament,
                saveTournament,
                resetForm,
                formatDate,
                formatDateRange
            }
        }
    }
</script>

<style scoped>
    .tournaments {
        min-height: 100vh;
    }

    .toolbar {
        margin-bottom: 20px;
    }

    .pagination {
        margin-top: 20px;
        text-align: right;
    }

    .tournament-detail {
        padding: 20px 0;
    }

    .dialog-footer {
        display: flex;
        justify-content: flex-end;
        gap: 10px;
    }

    /* 响应式设计 */
    @media (max-width: 768px) {
        .toolbar .el-col {
            margin-bottom: 10px;
        }

        .pagination {
            text-align: center;
        }
    }
</style>