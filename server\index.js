const express = require('express')
const mysql = require('mysql2/promise')
const cors = require('cors')
const path = require('path')

const app = express()
const PORT = process.env.PORT || 3001

// 中间件
app.use(cors())
app.use(express.json())
app.use(express.static(path.join(__dirname, '../dist')))

// 数据库连接配置
const dbConfig = {
    host: 'localhost',
    user: 'root',
    password: 'root',
    database: 'football_youth',
    charset: 'utf8mb4'
}

// 创建数据库连接池
const pool = mysql.createPool(dbConfig)

// 测试数据库连接
async function testConnection () {
    try {
        const connection = await pool.getConnection()
        console.log('数据库连接成功!')
        connection.release()
    } catch (error) {
        console.error('数据库连接失败:', error)
    }
}

// 赛事相关API
app.get('/api/tournaments', async (req, res) => {
    try {
        const [rows] = await pool.execute('SELECT * FROM tournaments ORDER BY created_at DESC')
        res.json(rows)
    } catch (error) {
        console.error('获取赛事列表失败:', error)
        res.status(500).json({ error: '获取赛事列表失败' })
    }
})

app.get('/api/tournaments/:id', async (req, res) => {
    try {
        const [rows] = await pool.execute('SELECT * FROM tournaments WHERE id = ?', [req.params.id])
        if (rows.length === 0) {
            return res.status(404).json({ error: '赛事不存在' })
        }
        res.json(rows[0])
    } catch (error) {
        console.error('获取赛事详情失败:', error)
        res.status(500).json({ error: '获取赛事详情失败' })
    }
})

// 球队相关API
app.get('/api/teams', async (req, res) => {
    try {
        const { tournament_id } = req.query
        let query = 'SELECT * FROM teams ORDER BY created_at DESC'
        let params = []

        if (tournament_id) {
            query = 'SELECT * FROM teams WHERE tournament_id = ? ORDER BY created_at DESC'
            params = [tournament_id]
        }

        const [rows] = await pool.execute(query, params)
        res.json(rows)
    } catch (error) {
        console.error('获取球队列表失败:', error)
        res.status(500).json({ error: '获取球队列表失败' })
    }
})

app.get('/api/teams/:id', async (req, res) => {
    try {
        const [rows] = await pool.execute('SELECT * FROM teams WHERE id = ?', [req.params.id])
        if (rows.length === 0) {
            return res.status(404).json({ error: '球队不存在' })
        }
        res.json(rows[0])
    } catch (error) {
        console.error('获取球队详情失败:', error)
        res.status(500).json({ error: '获取球队详情失败' })
    }
})

// 球员相关API
app.get('/api/players', async (req, res) => {
    try {
        const { team_id } = req.query
        let query = 'SELECT * FROM players ORDER BY created_at DESC'
        let params = []

        if (team_id) {
            query = 'SELECT * FROM players WHERE team_id = ? ORDER BY created_at DESC'
            params = [team_id]
        }

        const [rows] = await pool.execute(query, params)
        res.json(rows)
    } catch (error) {
        console.error('获取球员列表失败:', error)
        res.status(500).json({ error: '获取球员列表失败' })
    }
})

app.get('/api/players/:id', async (req, res) => {
    try {
        const [rows] = await pool.execute('SELECT * FROM players WHERE id = ?', [req.params.id])
        if (rows.length === 0) {
            return res.status(404).json({ error: '球员不存在' })
        }
        res.json(rows[0])
    } catch (error) {
        console.error('获取球员详情失败:', error)
        res.status(500).json({ error: '获取球员详情失败' })
    }
})

// 比赛相关API
app.get('/api/matches', async (req, res) => {
    try {
        const { tournament_id } = req.query
        let query = 'SELECT * FROM matches ORDER BY match_date DESC'
        let params = []

        if (tournament_id) {
            query = 'SELECT * FROM matches WHERE tournament_id = ? ORDER BY match_date DESC'
            params = [tournament_id]
        }

        const [rows] = await pool.execute(query, params)
        res.json(rows)
    } catch (error) {
        console.error('获取比赛列表失败:', error)
        res.status(500).json({ error: '获取比赛列表失败' })
    }
})

app.get('/api/matches/:id', async (req, res) => {
    try {
        const [rows] = await pool.execute('SELECT * FROM matches WHERE id = ?', [req.params.id])
        if (rows.length === 0) {
            return res.status(404).json({ error: '比赛不存在' })
        }
        res.json(rows[0])
    } catch (error) {
        console.error('获取比赛详情失败:', error)
        res.status(500).json({ error: '获取比赛详情失败' })
    }
})

// 进球相关API
app.get('/api/goals', async (req, res) => {
    try {
        const { match_id, player_id } = req.query
        let query = 'SELECT * FROM goals ORDER BY created_at DESC'
        let params = []

        if (match_id) {
            query = 'SELECT * FROM goals WHERE match_id = ? ORDER BY time_minute ASC'
            params = [match_id]
        } else if (player_id) {
            query = 'SELECT * FROM goals WHERE player_id = ? ORDER BY created_at DESC'
            params = [player_id]
        }

        const [rows] = await pool.execute(query, params)
        res.json(rows)
    } catch (error) {
        console.error('获取进球列表失败:', error)
        res.status(500).json({ error: '获取进球列表失败' })
    }
})

// 统计相关API
app.get('/api/statistics/overview', async (req, res) => {
    try {
        const [tournaments] = await pool.execute('SELECT COUNT(*) as count FROM tournaments')
        const [teams] = await pool.execute('SELECT COUNT(*) as count FROM teams')
        const [players] = await pool.execute('SELECT COUNT(*) as count FROM players')
        const [matches] = await pool.execute('SELECT COUNT(*) as count FROM matches')

        res.json({
            tournaments: tournaments[0].count,
            teams: teams[0].count,
            players: players[0].count,
            matches: matches[0].count
        })
    } catch (error) {
        console.error('获取统计数据失败:', error)
        res.status(500).json({ error: '获取统计数据失败' })
    }
})

// 用户相关API
app.get('/api/users', async (req, res) => {
    try {
        const [rows] = await pool.execute('SELECT id, username, email, role, last_login, created_at FROM users ORDER BY created_at DESC')
        res.json(rows)
    } catch (error) {
        console.error('获取用户列表失败:', error)
        res.status(500).json({ error: '获取用户列表失败' })
    }
})

// 处理Vue Router的history模式 - 只在生产环境中使用
if (process.env.NODE_ENV === 'production') {
    app.get('*', (req, res) => {
        res.sendFile(path.join(__dirname, '../dist/index.html'))
    })
}

// 启动服务器
app.listen(PORT, async () => {
    console.log(`服务器运行在 http://localhost:${PORT}`)
    await testConnection()
})

module.exports = app
