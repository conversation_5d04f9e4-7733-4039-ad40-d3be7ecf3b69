<template>
  <div class="home">
    <div class="page-container">
      <h1 class="page-title">数据概览</h1>
      
      <!-- 统计卡片 -->
      <el-row :gutter="20" class="stats-cards">
        <el-col :xs="12" :sm="6" :md="6" :lg="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon tournaments">
                <el-icon><Trophy /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ statistics.tournaments || 0 }}</div>
                <div class="stat-label">赛事总数</div>
              </div>
            </div>
          </el-card>
        </el-col>
        
        <el-col :xs="12" :sm="6" :md="6" :lg="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon teams">
                <el-icon><UserFilled /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ statistics.teams || 0 }}</div>
                <div class="stat-label">球队总数</div>
              </div>
            </div>
          </el-card>
        </el-col>
        
        <el-col :xs="12" :sm="6" :md="6" :lg="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon players">
                <el-icon><Avatar /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ statistics.players || 0 }}</div>
                <div class="stat-label">球员总数</div>
              </div>
            </div>
          </el-card>
        </el-col>
        
        <el-col :xs="12" :sm="6" :md="6" :lg="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon matches">
                <el-icon><Football /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ statistics.matches || 0 }}</div>
                <div class="stat-label">比赛总数</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 最新比赛结果 -->
      <el-row :gutter="20" class="content-section">
        <el-col :xs="24" :sm="12" :md="12" :lg="12">
          <el-card>
            <template #header>
              <div class="card-header">
                <span>最新比赛结果</span>
                <el-button type="text" @click="$router.push('/matches')">查看更多</el-button>
              </div>
            </template>
            <div v-if="recentMatches.length === 0" class="empty-state">
              <el-empty description="暂无比赛数据" />
            </div>
            <div v-else>
              <div 
                v-for="match in recentMatches" 
                :key="match.id" 
                class="match-item"
              >
                <div class="match-teams">
                  <span class="team-name">{{ getTeamName(match.home_team_id) }}</span>
                  <span class="match-score">{{ match.home_score }} : {{ match.away_score }}</span>
                  <span class="team-name">{{ getTeamName(match.away_team_id) }}</span>
                </div>
                <div class="match-info">
                  <span class="match-date">{{ formatDate(match.match_date) }}</span>
                  <el-tag :type="getMatchStatusType(match.status)" size="small">
                    {{ getMatchStatusText(match.status) }}
                  </el-tag>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>

        <!-- 热门球员排行 -->
        <el-col :xs="24" :sm="12" :md="12" :lg="12">
          <el-card>
            <template #header>
              <div class="card-header">
                <span>进球排行榜</span>
                <el-button type="text" @click="$router.push('/players')">查看更多</el-button>
              </div>
            </template>
            <div v-if="topPlayers.length === 0" class="empty-state">
              <el-empty description="暂无球员数据" />
            </div>
            <div v-else>
              <div 
                v-for="(player, index) in topPlayers" 
                :key="player.id" 
                class="player-item"
              >
                <div class="player-rank">{{ index + 1 }}</div>
                <div class="player-info">
                  <div class="player-name">{{ player.name }}</div>
                  <div class="player-team">{{ getTeamName(player.team_id) }}</div>
                </div>
                <div class="player-goals">{{ player.goals || 0 }}球</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 即将开始的比赛 -->
      <el-row class="content-section">
        <el-col :span="24">
          <el-card>
            <template #header>
              <div class="card-header">
                <span>即将开始的比赛</span>
                <el-button type="text" @click="$router.push('/matches')">查看更多</el-button>
              </div>
            </template>
            <div v-if="upcomingMatches.length === 0" class="empty-state">
              <el-empty description="暂无即将开始的比赛" />
            </div>
            <div v-else>
              <el-row :gutter="16">
                <el-col 
                  v-for="match in upcomingMatches" 
                  :key="match.id"
                  :xs="24" :sm="12" :md="8" :lg="6"
                >
                  <div class="upcoming-match">
                    <div class="match-date">{{ formatDate(match.match_date) }}</div>
                    <div class="match-teams">
                      <div class="team">{{ getTeamName(match.home_team_id) }}</div>
                      <div class="vs">VS</div>
                      <div class="team">{{ getTeamName(match.away_team_id) }}</div>
                    </div>
                    <div class="match-location">{{ match.location || '待定' }}</div>
                  </div>
                </el-col>
              </el-row>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script>
import { ref, onMounted, computed } from 'vue'
import { useDataStore } from '@/stores'
import { tournamentAPI, teamAPI, playerAPI, matchAPI, statisticsAPI } from '@/api'

export default {
  name: 'Home',
  setup() {
    const dataStore = useDataStore()
    
    const statistics = ref({})
    const recentMatches = ref([])
    const topPlayers = ref([])
    const upcomingMatches = ref([])
    
    const teams = computed(() => dataStore.teams)
    
    // 获取球队名称
    const getTeamName = (teamId) => {
      const team = teams.value.find(t => t.id === teamId)
      return team ? team.name : '未知球队'
    }
    
    // 格式化日期
    const formatDate = (dateString) => {
      const date = new Date(dateString)
      return date.toLocaleDateString('zh-CN', {
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      })
    }
    
    // 获取比赛状态类型
    const getMatchStatusType = (status) => {
      const statusMap = {
        'scheduled': '',
        'in_progress': 'warning',
        'completed': 'success',
        'cancelled': 'danger'
      }
      return statusMap[status] || ''
    }
    
    // 获取比赛状态文本
    const getMatchStatusText = (status) => {
      const statusMap = {
        'scheduled': '未开始',
        'in_progress': '进行中',
        'completed': '已结束',
        'cancelled': '已取消'
      }
      return statusMap[status] || status
    }
    
    // 加载数据
    const loadData = async () => {
      try {
        // 加载统计数据
        const statsData = await statisticsAPI.getOverview()
        statistics.value = statsData
        
        // 加载球队数据
        const teamsData = await teamAPI.getAll()
        dataStore.setTeams(teamsData)
        
        // 加载最新比赛
        const matchesData = await matchAPI.getAll()
        recentMatches.value = matchesData
          .filter(m => m.status === 'completed')
          .sort((a, b) => new Date(b.match_date) - new Date(a.match_date))
          .slice(0, 5)
        
        // 加载即将开始的比赛
        upcomingMatches.value = matchesData
          .filter(m => m.status === 'scheduled' && new Date(m.match_date) > new Date())
          .sort((a, b) => new Date(a.match_date) - new Date(b.match_date))
          .slice(0, 4)
        
        // 加载球员数据
        const playersData = await playerAPI.getAll()
        topPlayers.value = playersData
          .map(player => ({
            ...player,
            goals: player.metadata?.goals || 0
          }))
          .sort((a, b) => b.goals - a.goals)
          .slice(0, 5)
          
      } catch (error) {
        console.error('加载数据失败:', error)
      }
    }
    
    onMounted(() => {
      loadData()
    })
    
    return {
      statistics,
      recentMatches,
      topPlayers,
      upcomingMatches,
      getTeamName,
      formatDate,
      getMatchStatusType,
      getMatchStatusText
    }
  }
}
</script>

<style scoped>
.home {
  min-height: 100vh;
}

.stats-cards {
  margin-bottom: 20px;
}

.stat-card {
  margin-bottom: 20px;
}

.stat-content {
  display: flex;
  align-items: center;
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  font-size: 24px;
  color: white;
}

.stat-icon.tournaments {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-icon.teams {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stat-icon.players {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stat-icon.matches {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stat-info {
  flex: 1;
}

.stat-number {
  font-size: 28px;
  font-weight: 600;
  color: #303133;
  line-height: 1;
}

.stat-label {
  font-size: 14px;
  color: #909399;
  margin-top: 4px;
}

.content-section {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.empty-state {
  padding: 20px 0;
}

.match-item {
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.match-item:last-child {
  border-bottom: none;
}

.match-teams {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.team-name {
  font-weight: 500;
  color: #303133;
}

.match-score {
  font-size: 18px;
  font-weight: 600;
  color: #409eff;
}

.match-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  color: #909399;
}

.player-item {
  display: flex;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.player-item:last-child {
  border-bottom: none;
}

.player-rank {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background-color: #409eff;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  margin-right: 12px;
}

.player-info {
  flex: 1;
}

.player-name {
  font-weight: 500;
  color: #303133;
}

.player-team {
  font-size: 12px;
  color: #909399;
  margin-top: 2px;
}

.player-goals {
  font-weight: 600;
  color: #409eff;
}

.upcoming-match {
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 16px;
  text-align: center;
  margin-bottom: 16px;
}

.upcoming-match .match-date {
  font-size: 12px;
  color: #909399;
  margin-bottom: 8px;
}

.upcoming-match .match-teams {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 8px;
}

.upcoming-match .team {
  font-weight: 500;
  color: #303133;
  font-size: 14px;
}

.upcoming-match .vs {
  margin: 0 8px;
  color: #909399;
  font-size: 12px;
}

.upcoming-match .match-location {
  font-size: 12px;
  color: #909399;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .stat-number {
    font-size: 24px;
  }
  
  .match-teams {
    flex-direction: column;
    text-align: center;
  }
  
  .match-score {
    margin: 4px 0;
  }
}
</style>
