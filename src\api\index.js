import axios from 'axios'

// 创建axios实例
const api = axios.create({
    baseURL: process.env.NODE_ENV === 'production' ? '/api' : 'http://localhost:3001/api',
    timeout: 10000,
    headers: {
        'Content-Type': 'application/json'
    }
})

// 请求拦截器
api.interceptors.request.use(
    config => {
        // 可以在这里添加token等认证信息
        const token = localStorage.getItem('token')
        if (token) {
            config.headers.Authorization = `Bearer ${token}`
        }
        return config
    },
    error => {
        return Promise.reject(error)
    }
)

// 响应拦截器
api.interceptors.response.use(
    response => {
        return response.data
    },
    error => {
        console.error('API Error:', error)
        return Promise.reject(error)
    }
)

// 赛事相关API
export const tournamentAPI = {
    // 获取所有赛事
    getAll: () => api.get('/tournaments'),

    // 根据ID获取赛事
    getById: (id) => api.get(`/tournaments/${id}`),

    // 创建赛事
    create: (data) => api.post('/tournaments', data),

    // 更新赛事
    update: (id, data) => api.put(`/tournaments/${id}`, data),

    // 删除赛事
    delete: (id) => api.delete(`/tournaments/${id}`)
}

// 球队相关API
export const teamAPI = {
    getAll: () => api.get('/teams'),
    getById: (id) => api.get(`/teams/${id}`),
    getByTournament: (tournamentId) => api.get(`/teams?tournament_id=${tournamentId}`),
    create: (data) => api.post('/teams', data),
    update: (id, data) => api.put(`/teams/${id}`, data),
    delete: (id) => api.delete(`/teams/${id}`)
}

// 球员相关API
export const playerAPI = {
    getAll: () => api.get('/players'),
    getById: (id) => api.get(`/players/${id}`),
    getByTeam: (teamId) => api.get(`/players?team_id=${teamId}`),
    create: (data) => api.post('/players', data),
    update: (id, data) => api.put(`/players/${id}`, data),
    delete: (id) => api.delete(`/players/${id}`)
}

// 比赛相关API
export const matchAPI = {
    getAll: () => api.get('/matches'),
    getById: (id) => api.get(`/matches/${id}`),
    getByTournament: (tournamentId) => api.get(`/matches?tournament_id=${tournamentId}`),
    create: (data) => api.post('/matches', data),
    update: (id, data) => api.put(`/matches/${id}`, data),
    delete: (id) => api.delete(`/matches/${id}`)
}

// 进球相关API
export const goalAPI = {
    getAll: () => api.get('/goals'),
    getByMatch: (matchId) => api.get(`/goals?match_id=${matchId}`),
    getByPlayer: (playerId) => api.get(`/goals?player_id=${playerId}`),
    create: (data) => api.post('/goals', data),
    update: (id, data) => api.put(`/goals/${id}`, data),
    delete: (id) => api.delete(`/goals/${id}`)
}

// 用户相关API
export const userAPI = {
    getAll: () => api.get('/users'),
    getById: (id) => api.get(`/users/${id}`),
    create: (data) => api.post('/users', data),
    update: (id, data) => api.put(`/users/${id}`, data),
    delete: (id) => api.delete(`/users/${id}`),
    login: (credentials) => api.post('/auth/login', credentials),
    logout: () => api.post('/auth/logout')
}

// 统计相关API
export const statisticsAPI = {
    getOverview: () => api.get('/statistics/overview'),
    getPlayerStats: () => api.get('/statistics/players'),
    getTeamStats: () => api.get('/statistics/teams'),
    getMatchStats: () => api.get('/statistics/matches'),
    getTournamentStats: () => api.get('/statistics/tournaments')
}

export default api
