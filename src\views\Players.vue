<template>
  <div class="players">
    <div class="page-container">
      <h1 class="page-title">球员档案</h1>
      
      <!-- 搜索和操作栏 -->
      <div class="toolbar">
        <el-row :gutter="20" justify="space-between">
          <el-col :span="16">
            <el-input
              v-model="searchText"
              placeholder="搜索球员姓名或位置"
              clearable
              @input="handleSearch"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
          </el-col>
          <el-col :span="8" style="text-align: right">
            <el-button type="primary" @click="showAddDialog = true">
              <el-icon><Plus /></el-icon>
              新增球员
            </el-button>
          </el-col>
        </el-row>
      </div>

      <!-- 球员列表 -->
      <el-table 
        :data="filteredPlayers" 
        v-loading="loading"
        stripe
        style="width: 100%"
      >
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="name" label="姓名" width="120" />
        <el-table-column label="所属球队" width="200">
          <template #default="scope">
            {{ getTeamName(scope.row.team_id) }}
          </template>
        </el-table-column>
        <el-table-column prop="position" label="位置" width="100" />
        <el-table-column label="年龄" width="80">
          <template #default="scope">
            {{ calculateAge(scope.row.birth_date) }}
          </template>
        </el-table-column>
        <el-table-column prop="height" label="身高(cm)" width="100" />
        <el-table-column prop="weight" label="体重(kg)" width="100" />
        <el-table-column label="进球数" width="100">
          <template #default="scope">
            {{ scope.row.metadata?.goals || 0 }}
          </template>
        </el-table-column>
        <el-table-column prop="description" label="描述" min-width="200" show-overflow-tooltip />
        <el-table-column label="操作" width="180" fixed="right">
          <template #default="scope">
            <el-button size="small" @click="viewPlayer(scope.row)">查看</el-button>
            <el-button size="small" type="primary" @click="editPlayer(scope.row)">编辑</el-button>
            <el-button 
              size="small" 
              type="danger" 
              @click="deletePlayer(scope.row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="players.length"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 新增/编辑球员对话框 -->
    <el-dialog
      v-model="showAddDialog"
      :title="editingPlayer ? '编辑球员' : '新增球员'"
      width="600px"
      @close="resetForm"
    >
      <el-form
        ref="playerFormRef"
        :model="playerForm"
        :rules="formRules"
        label-width="100px"
      >
        <el-form-item label="球员姓名" prop="name">
          <el-input v-model="playerForm.name" placeholder="请输入球员姓名" />
        </el-form-item>
        
        <el-form-item label="所属球队" prop="team_id">
          <el-select v-model="playerForm.team_id" placeholder="请选择球队" style="width: 100%">
            <el-option
              v-for="team in teams"
              :key="team.id"
              :label="team.name"
              :value="team.id"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="位置" prop="position">
          <el-select v-model="playerForm.position" placeholder="请选择位置" style="width: 100%">
            <el-option label="门将" value="门将" />
            <el-option label="后卫" value="后卫" />
            <el-option label="中场" value="中场" />
            <el-option label="前锋" value="前锋" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="出生日期" prop="birth_date">
          <el-date-picker
            v-model="playerForm.birth_date"
            type="date"
            placeholder="选择出生日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            style="width: 100%"
          />
        </el-form-item>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="身高(cm)" prop="height">
              <el-input-number 
                v-model="playerForm.height" 
                :min="100" 
                :max="250"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="体重(kg)" prop="weight">
              <el-input-number 
                v-model="playerForm.weight" 
                :min="30" 
                :max="150"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-form-item label="球员描述" prop="description">
          <el-input
            v-model="playerForm.description"
            type="textarea"
            :rows="4"
            placeholder="请输入球员描述"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showAddDialog = false">取消</el-button>
          <el-button type="primary" @click="savePlayer" :loading="saving">
            {{ editingPlayer ? '更新' : '创建' }}
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 球员详情对话框 -->
    <el-dialog
      v-model="showDetailDialog"
      title="球员详情"
      width="800px"
    >
      <div v-if="selectedPlayer" class="player-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="球员姓名">
            {{ selectedPlayer.name }}
          </el-descriptions-item>
          <el-descriptions-item label="所属球队">
            {{ getTeamName(selectedPlayer.team_id) }}
          </el-descriptions-item>
          <el-descriptions-item label="位置">
            {{ selectedPlayer.position }}
          </el-descriptions-item>
          <el-descriptions-item label="年龄">
            {{ calculateAge(selectedPlayer.birth_date) }}岁
          </el-descriptions-item>
          <el-descriptions-item label="身高">
            {{ selectedPlayer.height }}cm
          </el-descriptions-item>
          <el-descriptions-item label="体重">
            {{ selectedPlayer.weight }}kg
          </el-descriptions-item>
          <el-descriptions-item label="进球数">
            {{ selectedPlayer.metadata?.goals || 0 }}
          </el-descriptions-item>
          <el-descriptions-item label="助攻数">
            {{ selectedPlayer.metadata?.assists || 0 }}
          </el-descriptions-item>
          <el-descriptions-item label="球员描述" :span="2">
            {{ selectedPlayer.description }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { playerAPI, teamAPI } from '@/api'
import { useDataStore } from '@/stores'

export default {
  name: 'PlayersPage',
  setup() {
    const dataStore = useDataStore()
    
    // 响应式数据
    const loading = ref(false)
    const saving = ref(false)
    const searchText = ref('')
    const currentPage = ref(1)
    const pageSize = ref(20)
    const showAddDialog = ref(false)
    const showDetailDialog = ref(false)
    const editingPlayer = ref(null)
    const selectedPlayer = ref(null)
    const playerFormRef = ref()
    
    const players = computed(() => dataStore.players)
    const teams = computed(() => dataStore.teams)
    
    // 表单数据
    const playerForm = reactive({
      name: '',
      team_id: null,
      position: '',
      birth_date: '',
      height: null,
      weight: null,
      description: ''
    })
    
    // 表单验证规则
    const formRules = {
      name: [
        { required: true, message: '请输入球员姓名', trigger: 'blur' }
      ],
      team_id: [
        { required: true, message: '请选择所属球队', trigger: 'change' }
      ],
      position: [
        { required: true, message: '请选择位置', trigger: 'change' }
      ]
    }
    
    // 计算属性
    const filteredPlayers = computed(() => {
      if (!searchText.value) return players.value
      
      return players.value.filter(player =>
        player.name.toLowerCase().includes(searchText.value.toLowerCase()) ||
        player.position.toLowerCase().includes(searchText.value.toLowerCase())
      )
    })
    
    // 方法
    const loadData = async () => {
      loading.value = true
      try {
        const [playersData, teamsData] = await Promise.all([
          playerAPI.getAll(),
          teamAPI.getAll()
        ])
        dataStore.setPlayers(playersData)
        dataStore.setTeams(teamsData)
      } catch (error) {
        ElMessage.error('加载数据失败')
        console.error('加载数据失败:', error)
      } finally {
        loading.value = false
      }
    }
    
    const getTeamName = (teamId) => {
      const team = teams.value.find(t => t.id === teamId)
      return team ? team.name : '未知球队'
    }
    
    const calculateAge = (birthDate) => {
      if (!birthDate) return ''
      const today = new Date()
      const birth = new Date(birthDate)
      let age = today.getFullYear() - birth.getFullYear()
      const monthDiff = today.getMonth() - birth.getMonth()
      if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
        age--
      }
      return age
    }
    
    const handleSearch = () => {
      currentPage.value = 1
    }
    
    const handleSizeChange = (val) => {
      pageSize.value = val
      currentPage.value = 1
    }
    
    const handleCurrentChange = (val) => {
      currentPage.value = val
    }
    
    const viewPlayer = (player) => {
      selectedPlayer.value = player
      showDetailDialog.value = true
    }
    
    const editPlayer = (player) => {
      editingPlayer.value = player
      playerForm.name = player.name
      playerForm.team_id = player.team_id
      playerForm.position = player.position
      playerForm.birth_date = player.birth_date
      playerForm.height = player.height
      playerForm.weight = player.weight
      playerForm.description = player.description
      showAddDialog.value = true
    }
    
    const deletePlayer = async (player) => {
      try {
        await ElMessageBox.confirm(
          `确定要删除球员"${player.name}"吗？`,
          '确认删除',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }
        )
        
        await playerAPI.delete(player.id)
        dataStore.deletePlayer(player.id)
        ElMessage.success('删除成功')
      } catch (error) {
        if (error !== 'cancel') {
          ElMessage.error('删除失败')
          console.error('删除球员失败:', error)
        }
      }
    }
    
    const savePlayer = async () => {
      if (!playerFormRef.value) return
      
      try {
        await playerFormRef.value.validate()
        saving.value = true
        
        const formData = {
          name: playerForm.name,
          team_id: playerForm.team_id,
          position: playerForm.position,
          birth_date: playerForm.birth_date,
          height: playerForm.height,
          weight: playerForm.weight,
          description: playerForm.description
        }
        
        if (editingPlayer.value) {
          // 更新
          const updatedPlayer = await playerAPI.update(editingPlayer.value.id, formData)
          dataStore.updatePlayer(editingPlayer.value.id, updatedPlayer)
          ElMessage.success('更新成功')
        } else {
          // 新增
          const newPlayer = await playerAPI.create(formData)
          dataStore.addPlayer(newPlayer)
          ElMessage.success('创建成功')
        }
        
        showAddDialog.value = false
        resetForm()
      } catch (error) {
        if (error.message) {
          ElMessage.error('保存失败')
          console.error('保存球员失败:', error)
        }
      } finally {
        saving.value = false
      }
    }
    
    const resetForm = () => {
      editingPlayer.value = null
      playerForm.name = ''
      playerForm.team_id = null
      playerForm.position = ''
      playerForm.birth_date = ''
      playerForm.height = null
      playerForm.weight = null
      playerForm.description = ''
      if (playerFormRef.value) {
        playerFormRef.value.resetFields()
      }
    }
    
    // 生命周期
    onMounted(() => {
      loadData()
    })
    
    return {
      loading,
      saving,
      searchText,
      currentPage,
      pageSize,
      showAddDialog,
      showDetailDialog,
      editingPlayer,
      selectedPlayer,
      playerFormRef,
      players,
      teams,
      playerForm,
      formRules,
      filteredPlayers,
      getTeamName,
      calculateAge,
      handleSearch,
      handleSizeChange,
      handleCurrentChange,
      viewPlayer,
      editPlayer,
      deletePlayer,
      savePlayer,
      resetForm
    }
  }
}
</script>

<style scoped>
.players {
  min-height: 100vh;
}

.toolbar {
  margin-bottom: 20px;
}

.pagination {
  margin-top: 20px;
  text-align: right;
}

.player-detail {
  padding: 20px 0;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .toolbar .el-col {
    margin-bottom: 10px;
  }
  
  .pagination {
    text-align: center;
  }
}
</style>
