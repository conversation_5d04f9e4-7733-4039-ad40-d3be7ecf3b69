// 错误处理工具

/**
 * 处理ResizeObserver错误
 * 这是一个常见的浏览器警告，通常不影响应用功能
 */
export function handleResizeObserverError() {
  // 捕获ResizeObserver错误
  const resizeObserverErr = (e) => {
    if (e.message && e.message.includes('ResizeObserver loop completed with undelivered notifications')) {
      // 忽略这个特定错误
      e.stopImmediatePropagation()
      return
    }
    // 其他错误正常处理
    console.error('Unhandled error:', e)
  }

  // 监听错误事件
  window.addEventListener('error', resizeObserverErr)
  
  // 监听未处理的Promise拒绝
  window.addEventListener('unhandledrejection', (e) => {
    if (e.reason && e.reason.message && e.reason.message.includes('ResizeObserver')) {
      e.preventDefault()
      return
    }
    console.error('Unhandled promise rejection:', e.reason)
  })
}

/**
 * 全局Vue错误处理器
 */
export function setupGlobalErrorHandler(app) {
  app.config.errorHandler = (err, vm, info) => {
    // 忽略ResizeObserver相关错误
    if (err.message && err.message.includes('ResizeObserver')) {
      return
    }
    
    // 忽略一些常见的无害警告
    const ignoredErrors = [
      'ResizeObserver loop completed',
      'Non-passive event listener',
      'Script error'
    ]
    
    const shouldIgnore = ignoredErrors.some(ignoredError => 
      err.message && err.message.includes(ignoredError)
    )
    
    if (shouldIgnore) {
      return
    }
    
    // 记录其他错误
    console.error('Vue Global Error:', {
      error: err,
      component: vm,
      info: info,
      timestamp: new Date().toISOString()
    })
    
    // 在开发环境中显示更详细的错误信息
    if (process.env.NODE_ENV === 'development') {
      console.group('🚨 Vue Error Details')
      console.error('Error:', err)
      console.error('Component:', vm)
      console.error('Info:', info)
      console.groupEnd()
    }
  }
  
  // 处理Vue警告
  app.config.warnHandler = (msg, vm, trace) => {
    // 忽略一些常见的无害警告
    const ignoredWarnings = [
      'ResizeObserver',
      'passive event listener'
    ]
    
    const shouldIgnore = ignoredWarnings.some(ignoredWarning => 
      msg.includes(ignoredWarning)
    )
    
    if (shouldIgnore) {
      return
    }
    
    // 在开发环境中显示警告
    if (process.env.NODE_ENV === 'development') {
      console.warn('Vue Warning:', msg)
      console.warn('Component:', vm)
      console.warn('Trace:', trace)
    }
  }
}

/**
 * 初始化所有错误处理
 */
export function initErrorHandling(app) {
  handleResizeObserverError()
  setupGlobalErrorHandler(app)
  
  console.log('✅ Error handling initialized')
}
